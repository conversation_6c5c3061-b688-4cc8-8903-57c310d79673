using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LineWebServer.Infra.Migrations
{
    /// <inheritdoc />
    public partial class QcTypeRefactorMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_qc_alters_qcs_QcId",
                table: "qc_alters");

            migrationBuilder.DropForeignKey(
                name: "FK_qc_rejects_qcs_QcId",
                table: "qc_rejects");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "qcs");

            migrationBuilder.AddColumn<long>(
                name: "OrderId",
                table: "station_tasks",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TaskId",
                table: "qcs",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AlterColumn<long>(
                name: "QcId",
                table: "qc_rejects",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AlterColumn<long>(
                name: "QcId",
                table: "qc_alters",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AddForeignKey(
                name: "FK_qc_alters_qcs_QcId",
                table: "qc_alters",
                column: "QcId",
                principalTable: "qcs",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_qc_rejects_qcs_QcId",
                table: "qc_rejects",
                column: "QcId",
                principalTable: "qcs",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_qc_alters_qcs_QcId",
                table: "qc_alters");

            migrationBuilder.DropForeignKey(
                name: "FK_qc_rejects_qcs_QcId",
                table: "qc_rejects");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "station_tasks");

            migrationBuilder.DropColumn(
                name: "TaskId",
                table: "qcs");

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "qcs",
                type: "varchar(30)",
                maxLength: 30,
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<long>(
                name: "QcId",
                table: "qc_rejects",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AlterColumn<long>(
                name: "QcId",
                table: "qc_alters",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_qc_alters_qcs_QcId",
                table: "qc_alters",
                column: "QcId",
                principalTable: "qcs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_qc_rejects_qcs_QcId",
                table: "qc_rejects",
                column: "QcId",
                principalTable: "qcs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
