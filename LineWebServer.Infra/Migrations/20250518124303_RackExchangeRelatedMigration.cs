using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LineWebServer.Infra.Migrations
{
    /// <inheritdoc />
    public partial class RackExchangeRelatedMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "OrgRackExchangeId",
                table: "rack_exchanges",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "ParentId",
                table: "rack_exchanges",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderId",
                table: "orders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "station_tasks",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    StationId = table.Column<long>(type: "bigint", nullable: false),
                    AmountType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Amount = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_station_tasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_station_tasks_stations_StationId",
                        column: x => x.StationId,
                        principalTable: "stations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_rack_exchanges_ParentId",
                table: "rack_exchanges",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_station_tasks_StationId",
                table: "station_tasks",
                column: "StationId");

            migrationBuilder.AddForeignKey(
                name: "FK_rack_exchanges_rack_exchanges_ParentId",
                table: "rack_exchanges",
                column: "ParentId",
                principalTable: "rack_exchanges",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_rack_exchanges_rack_exchanges_ParentId",
                table: "rack_exchanges");

            migrationBuilder.DropTable(
                name: "station_tasks");

            migrationBuilder.DropIndex(
                name: "IX_rack_exchanges_ParentId",
                table: "rack_exchanges");

            migrationBuilder.DropColumn(
                name: "OrgRackExchangeId",
                table: "rack_exchanges");

            migrationBuilder.DropColumn(
                name: "ParentId",
                table: "rack_exchanges");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "orders");
        }
    }
}
