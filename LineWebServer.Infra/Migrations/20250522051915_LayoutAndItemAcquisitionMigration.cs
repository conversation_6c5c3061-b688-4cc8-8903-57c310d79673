using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LineWebServer.Infra.Migrations
{
    /// <inheritdoc />
    public partial class LayoutAndItemAcquisitionMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "rack_exchanges");

            migrationBuilder.AddColumn<long>(
                name: "TaskId",
                table: "station_productions",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "OrderId",
                table: "orders",
                type: "varchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "Number",
                table: "layout_items",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "item_acquisitions",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    OrgTransitionId = table.Column<long>(type: "bigint", nullable: false),
                    OrderId = table.Column<long>(type: "bigint", nullable: false),
                    SupervisorId = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AmountType = table.Column<string>(type: "varchar(30)", maxLength: 30, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Amount = table.Column<int>(type: "int", nullable: false),
                    BundleSize = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_item_acquisitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_item_acquisitions_orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "item_releases",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ItemAcquisitionId = table.Column<long>(type: "bigint", nullable: false),
                    AmountType = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ItemQty = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_item_releases", x => x.Id);
                    table.ForeignKey(
                        name: "FK_item_releases_item_acquisitions_ItemAcquisitionId",
                        column: x => x.ItemAcquisitionId,
                        principalTable: "item_acquisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "station_tasks",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    StationId = table.Column<long>(type: "bigint", nullable: false),
                    ItemAcquisitionId = table.Column<long>(type: "bigint", nullable: false),
                    ItemReleaseId = table.Column<long>(type: "bigint", nullable: false),
                    AmountType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ItemQty = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_station_tasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StationTask_ItemAcquisition_Custom",
                        column: x => x.ItemAcquisitionId,
                        principalTable: "item_acquisitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StationTask_ItemRelease_Custom",
                        column: x => x.ItemReleaseId,
                        principalTable: "item_releases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StationTask_Station_Custom",
                        column: x => x.StationId,
                        principalTable: "stations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_station_productions_TaskId",
                table: "station_productions",
                column: "TaskId");

            migrationBuilder.CreateIndex(
                name: "IX_item_acquisitions_OrderId",
                table: "item_acquisitions",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_item_releases_ItemAcquisitionId",
                table: "item_releases",
                column: "ItemAcquisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_station_tasks_ItemAcquisitionId",
                table: "station_tasks",
                column: "ItemAcquisitionId");

            migrationBuilder.CreateIndex(
                name: "IX_station_tasks_ItemReleaseId",
                table: "station_tasks",
                column: "ItemReleaseId");

            migrationBuilder.CreateIndex(
                name: "IX_station_tasks_StationId",
                table: "station_tasks",
                column: "StationId");

            migrationBuilder.AddForeignKey(
                name: "FK_station_productions_station_tasks_TaskId",
                table: "station_productions",
                column: "TaskId",
                principalTable: "station_tasks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_station_productions_station_tasks_TaskId",
                table: "station_productions");

            migrationBuilder.DropTable(
                name: "station_tasks");

            migrationBuilder.DropTable(
                name: "item_releases");

            migrationBuilder.DropTable(
                name: "item_acquisitions");

            migrationBuilder.DropIndex(
                name: "IX_station_productions_TaskId",
                table: "station_productions");

            migrationBuilder.DropColumn(
                name: "TaskId",
                table: "station_productions");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "Number",
                table: "layout_items");

            migrationBuilder.CreateTable(
                name: "rack_exchanges",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    OrderId = table.Column<long>(type: "bigint", nullable: false),
                    Amount = table.Column<int>(type: "int", nullable: false),
                    AmountType = table.Column<string>(type: "varchar(30)", maxLength: 30, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ExchangeType = table.Column<string>(type: "varchar(30)", maxLength: 30, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Status = table.Column<string>(type: "varchar(30)", maxLength: 30, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SupervisorId = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_rack_exchanges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_rack_exchanges_orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_rack_exchanges_OrderId",
                table: "rack_exchanges",
                column: "OrderId");
        }
    }
}
