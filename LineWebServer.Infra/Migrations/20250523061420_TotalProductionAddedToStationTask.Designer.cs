// <auto-generated />
using System;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace LineWebServer.Infra.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250523061420_TotalProductionAddedToStationTask")]
    partial class TotalProductionAddedToStationTask
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("Domain.Entities.Event", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<int>("Floor")
                        .HasColumnType("int");

                    b.Property<int>("Line")
                        .HasColumnType("int");

                    b.Property<string>("Payload")
                        .HasMaxLength(1000)
                        .HasColumnType("text");

                    b.Property<int>("Station")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.ToTable("event", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.HourlyOutput", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("Alter")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Production")
                        .HasColumnType("int");

                    b.Property<int>("Reject")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("hourly_outputs", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ItemAcquisition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<string>("AmountType")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("BundleSize")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("OrgTransitionId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupervisorId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("item_acquisitions", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.ItemRelease", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AmountType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("ItemAcquisitionId")
                        .HasColumnType("bigint");

                    b.Property<int>("ItemQty")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ItemAcquisitionId");

                    b.ToTable("item_releases", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Layout", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.ToTable("layouts", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.LayoutItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("LayoutId")
                        .HasColumnType("bigint");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<long>("StationId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("LayoutId");

                    b.HasIndex("StationId");

                    b.ToTable("layout_items", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Order", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("BuyerName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("HourlyTarget")
                        .HasColumnType("int");

                    b.Property<string>("Label")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OrderQuantity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.ToTable("orders", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Qc", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("qcs", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.QcAlter", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("QcId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("WorkerId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("QcId");

                    b.ToTable("qc_alters", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.QcReject", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("QcId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("WorkerId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("QcId");

                    b.ToTable("qc_rejects", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Setting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("settings", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Station", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CurrentMode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.HasKey("Id");

                    b.ToTable("stations", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.StationMode", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Mode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<DateTime>("StartedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("StartedAt"));

                    b.Property<long>("StationId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("station_modes", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.StationProduction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("CreatedAt"));

                    b.Property<long>("StationId")
                        .HasColumnType("bigint");

                    b.Property<string>("StationType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)")
                        .HasDefaultValue("default");

                    b.Property<long>("StationWorkerId")
                        .HasColumnType("bigint");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("WorkerId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TaskId");

                    b.ToTable("station_productions", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.StationTask", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AmountType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime?>("CreatedAt"));

                    b.Property<long>("ItemAcquisitionId")
                        .HasColumnType("bigint");

                    b.Property<int>("ItemQty")
                        .HasColumnType("int");

                    b.Property<long>("ItemReleaseId")
                        .HasColumnType("bigint");

                    b.Property<long>("StationId")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("TotalProduction")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ItemAcquisitionId");

                    b.HasIndex("ItemReleaseId");

                    b.HasIndex("StationId");

                    b.ToTable("station_tasks", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.StationWorker", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("StartedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime(6)");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<DateTime>("StartedAt"));

                    b.Property<long>("StationId")
                        .HasColumnType("bigint");

                    b.Property<string>("WorkerId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("WorkerName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.ToTable("station_workers", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.HourlyOutput", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("HourlyOutputs")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Domain.Entities.ItemAcquisition", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("ItemAcquisitions")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Domain.Entities.ItemRelease", b =>
                {
                    b.HasOne("Domain.Entities.ItemAcquisition", "ItemAcquisition")
                        .WithMany()
                        .HasForeignKey("ItemAcquisitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemAcquisition");
                });

            modelBuilder.Entity("Domain.Entities.LayoutItem", b =>
                {
                    b.HasOne("Domain.Entities.Layout", "Layout")
                        .WithMany("LayoutItems")
                        .HasForeignKey("LayoutId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Station", "Station")
                        .WithMany()
                        .HasForeignKey("StationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Layout");

                    b.Navigation("Station");
                });

            modelBuilder.Entity("Domain.Entities.Qc", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("Qcs")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Domain.Entities.QcAlter", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("QcAlters")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Qc", "Qc")
                        .WithMany()
                        .HasForeignKey("QcId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Qc");
                });

            modelBuilder.Entity("Domain.Entities.QcReject", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("QcRejects")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Qc", "Qc")
                        .WithMany()
                        .HasForeignKey("QcId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Qc");
                });

            modelBuilder.Entity("Domain.Entities.StationProduction", b =>
                {
                    b.HasOne("Domain.Entities.StationTask", null)
                        .WithMany()
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Entities.StationTask", b =>
                {
                    b.HasOne("Domain.Entities.ItemAcquisition", "ItemAcquisition")
                        .WithMany("StationTasks")
                        .HasForeignKey("ItemAcquisitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_StationTask_ItemAcquisition_Custom");

                    b.HasOne("Domain.Entities.ItemRelease", "ItemRelease")
                        .WithMany("StationTasks")
                        .HasForeignKey("ItemReleaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_StationTask_ItemRelease_Custom");

                    b.HasOne("Domain.Entities.Station", "Station")
                        .WithMany("StationTasks")
                        .HasForeignKey("StationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_StationTask_Station_Custom");

                    b.Navigation("ItemAcquisition");

                    b.Navigation("ItemRelease");

                    b.Navigation("Station");
                });

            modelBuilder.Entity("Domain.Entities.ItemAcquisition", b =>
                {
                    b.Navigation("StationTasks");
                });

            modelBuilder.Entity("Domain.Entities.ItemRelease", b =>
                {
                    b.Navigation("StationTasks");
                });

            modelBuilder.Entity("Domain.Entities.Layout", b =>
                {
                    b.Navigation("LayoutItems");
                });

            modelBuilder.Entity("Domain.Entities.Order", b =>
                {
                    b.Navigation("HourlyOutputs");

                    b.Navigation("ItemAcquisitions");

                    b.Navigation("QcAlters");

                    b.Navigation("QcRejects");

                    b.Navigation("Qcs");
                });

            modelBuilder.Entity("Domain.Entities.Station", b =>
                {
                    b.Navigation("StationTasks");
                });
#pragma warning restore 612, 618
        }
    }
}
