using Domain.CommonResource;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Extensions;

public static class QueryBuilderExtensions
{
    public static async Task<CommonPaginationResource<T>> Paginate<T>(this IQueryable<T> query, int page, int perPage)
    {
        int totalCount = await query.CountAsync();
        int totalPages = (int)Math.Ceiling((double)totalCount / perPage);
        query = query.Skip((page - 1) * perPage).Take(perPage);

        return new CommonPaginationResource<T>
        {
            Items = await query.ToListAsync(),
            TotalCount = totalCount,
            TotalPages = totalPages,
            Page = page,
            PerPage = perPage
        };
    }
}
