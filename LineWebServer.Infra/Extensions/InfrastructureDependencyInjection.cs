using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using LineWebServer.Infra.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace LineWebServer.Infra.Extensions;

public static class InfrastructureDependencyInjection
{
    public static IServiceCollection AddServicesFromInfra(this IServiceCollection services, IConfiguration config)
    {
        services.AddHealthChecks().AddDbContextCheck<ApplicationDbContext>();
        
        IConfigurationSection connectionString = config.GetSection("DefaultConnection")
                                                 ?? throw new InvalidOperationException("Connection string:" + "'DefaultConnection' not found.");

        MySqlServerVersion serverVersion = new MySqlServerVersion(new Version(9, 2, 0));

        services.AddDbContext<ApplicationDbContext>(options =>
            options
                .UseMySql(connectionString.Value, serverVersion)
                .EnableSensitiveDataLogging()
                .EnableDetailedErrors(false)
        );
        
        services.AddTransient<IEventRepository, EventRepository>();
        services.AddTransient<IHourlyOutputRepository, HourlyOutputRepository>();
        services.AddTransient<ILayoutRepository, LayoutRepository>();
        services.AddTransient<ILineMonitorRepository, LineMonitorRepository>();
        services.AddTransient<ISupervisorOrderRepository, SupervisorOrderRepository>();
        services.AddTransient<IQcRepository, QcRepository>();
        services.AddTransient<IStationModeRepository, StationModeRepository>();
        services.AddTransient<IStationProductionRepository, StationProductionRepository>();
        services.AddTransient<IStationRepository, StationRepository>();
        services.AddTransient<IStationWorkerRepository, StationWorkerRepository>();
        services.AddTransient<ISettingRepository, SettingRepository>();
        services.AddTransient<IItemAcquisitionRepository, ItemAcquisitionRepository>();
        services.AddTransient<IStationTaskRepository, StationTaskRepository>();
        services.AddTransient<ISwingRepository, SwingRepository>();
        services.AddTransient<IUnitOfWork, UnitOfWork>();

        return services;
    }
}
