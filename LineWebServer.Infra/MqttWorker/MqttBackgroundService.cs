using System.Text;
using System.Text.RegularExpressions;
using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using Hangfire;
using LineWebServer.Application.Configs;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Jobs;
using LineWebServer.Jobs;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Exceptions;
using MQTTnet.Protocol;

namespace LineWebServer.Infra.MqttWorker;

public class MqttBackgroundService(
    ILogger<MqttBackgroundService> logger,
    IOptions<MqttConfig> mqttConfig,
    IOptions<List<FloorConfig>> floorConfig,
    IOptions<PpmsApiConfig> ppmsApiConfig,
    IServiceProvider serviceProvider,
    IChannelQueueInterface<PublishToBrokerRequest> brokerMessageQueue)
    : BackgroundService
{
    private readonly ILogger<MqttBackgroundService> _logger = logger;
    private readonly MqttConfig _mqttConfig = mqttConfig.Value;
    private readonly PpmsApiConfig _ppmsApiConfig = ppmsApiConfig.Value;
    private readonly List<FloorConfig> _floorConfig = floorConfig.Value;
    private readonly List<Task> _runningTasks = new();
    private readonly IServiceProvider _serviceProvider = serviceProvider;
    private readonly SemaphoreSlim _semaphoreSlim = new(1, 1);

    private async Task SubscribeToTopics(IMqttClient mqttClient, int floorNo, int lineNo, int totalStations, CancellationToken stoppingToken)
    {
        var stationNumbers = Enumerable.Range(1, totalStations).ToList();

        foreach (int station in stationNumbers)
        {
            
            var topics = new[]
            {
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_rfid_set",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_pd_mode",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_alter_mode",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_maintenance_mode",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_sw",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_stat_details",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_rack_exchange",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/on_log",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/_auth",
                $"floor/{floorNo}/line/{lineNo}/st/{station}/_st_mode",
            };

            foreach (string topic in topics)
            {
                var mqttSubscribeOptions = new MqttTopicFilterBuilder()
                    .WithTopic(topic)
                    .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce)
                    .Build();

                await mqttClient.SubscribeAsync(mqttSubscribeOptions, stoppingToken);
            }
        }

        _logger.LogInformation("MQTT client subscribed to topics for floor:{floor}, line:{line}", floorNo, lineNo);
    }

    private async Task ProcessIncomingData(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            var match = Regex.Match(e.ApplicationMessage.Topic, @"floor/(\d+)/line/(\d+)/st/(\d+)/(.+)");

            if (!match.Success)
            {
                _logger.LogError("Invalid topic format: {topic}", e.ApplicationMessage.Topic);
                return;
            }

            int floor = int.Parse(match.Groups[1].Value);
            int line = int.Parse(match.Groups[2].Value);
            int station = int.Parse(match.Groups[3].Value);
            string eventType = match.Groups[4].Value;

            _logger.LogInformation("Data received: Floor: {floor}, Line: {line}, Station: {station}, Event: {eventType}",
                floor, line, station, eventType);

            string? payload = null;
            var payloadSegment = e.ApplicationMessage.PayloadSegment;
            if (payloadSegment.Count > 0)
            {
                payload = Encoding.UTF8.GetString(payloadSegment.Array ?? Array.Empty<byte>(),
                    payloadSegment.Offset, payloadSegment.Count);
            }

            var @event = new Event
            {
                Type = eventType,
                Payload = payload,
                Floor = floor,
                Line = line,
                Station = station,
                CreatedAt = DateTime.Now
            };

            using var scope = _serviceProvider.CreateScope();
            var repository = scope.ServiceProvider.GetRequiredService<IEventRepository>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            await repository.Create(@event);
            await unitOfWork.SaveChangesAsync();

            switch (eventType)
            {
                case EventTypeEnum.OnRfidSet:
                    BackgroundJob.Enqueue<HandleOnRfidEventJob>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnProductionMode:
                    BackgroundJob.Enqueue<HandleOnProductionModeEventJob>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnAlterMode:
                    BackgroundJob.Enqueue<HandleOnAlterModeEventJob>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnMaintenanceMode:
                    BackgroundJob.Enqueue<HandleMaintenanceModeEventJob>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnProductionSwitch:
                    BackgroundJob.Enqueue<HandleStationProductionSwitchEvent>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnStatDetails:
                    BackgroundJob.Enqueue<HandleOnStatDetailsEventJob>(j =>  j.Handle(@event));
                    break;
                case EventTypeEnum.OnRackExchange:
                    // TODO: Implement org mqtt to communicate between line server and org server
                    if (payload != null)
                    {
                        string[] parts = payload.Split(',');
                        BackgroundJob.Enqueue<HandleAcceptApprovedBundleFromCuttingJob>(j =>  j.Handle(long.Parse(parts[0]), parts[1]));
                    }
                    break;
                case EventTypeEnum.OnNeedAuth:
                    BackgroundJob.Enqueue<HandleOnNeedAuthJob>(j =>  j.Handle(floor, line, station));
                    break;
                case EventTypeEnum.OnNeedStMode:
                    BackgroundJob.Enqueue<HandleOnNeedStModeJob>(j =>  j.Handle(floor, line, station));
                    break;
                case EventTypeEnum.OnError:
                    if (payload != null)
                    {
                        BackgroundJob.Enqueue<HandleOnErrorJob>(j =>  j.Handle(floor, line, station, payload));
                    }
                    break;
                default:
                    BackgroundJob.Enqueue<HandleUnknownEventJob>(j =>  j.Handle(@event));
                    break;
            }

            _logger.LogInformation("Event and job created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing incoming data");
        }
    }

    private async Task TryEstablishConnectionWithMqttBroker(IMqttClient mqttClient, MqttClientOptions mqttClientOptions, CancellationToken stoppingToken)
    {
        try
        {
            await _semaphoreSlim.WaitAsync(stoppingToken);
            
            await mqttClient.ConnectAsync(mqttClientOptions, stoppingToken);
            
            _semaphoreSlim.Release();
        }
        catch (Exception)
        {
            _semaphoreSlim.Release();
            throw;
        }
    }

    private async Task ConnectToMqttBroker(IMqttClient mqttClient, int floorNo, int lineNo, CancellationToken stoppingToken)
    {
        var mqttClientOptions = new MqttClientOptionsBuilder()
            .WithTcpServer(_mqttConfig.Host, _mqttConfig.Port)
            .WithClientId($"line-server-{floorNo}-{lineNo}")
            .WithCleanSession(false)
            .WithKeepAlivePeriod(TimeSpan.FromSeconds(15))
            .Build();

        await TryEstablishConnectionWithMqttBroker(mqttClient, mqttClientOptions, stoppingToken);

        _logger.LogInformation("MQTT client connected for floor:{floor}, line:{line}", floorNo, lineNo);

        var floor = _floorConfig.Find(f => f.No == floorNo);
        var line = floor?.Lines.Find(l => l.No == lineNo);

        if (floor == null || line == null)
        {
            throw new InvalidOperationException($"Floor {floorNo} or Line {lineNo} not found in configuration.");
        }

        await SubscribeToTopics(mqttClient, floorNo, lineNo, line.TotalStations, stoppingToken);
    }

    private async Task MakeLongRunningMqttReceiverClient(int floorNo, int lineNo, CancellationToken stoppingToken)
    {
        try
        {
            var factory = new MqttFactory();
            var mqttClient = factory.CreateMqttClient();

            mqttClient.ApplicationMessageReceivedAsync += ProcessIncomingData;
            mqttClient.DisconnectedAsync += async e =>
            {
                try
                {
                    if (!stoppingToken.IsCancellationRequested)
                    {
                        await Task.Delay(500, stoppingToken);
                        await ConnectToMqttBroker(mqttClient, floorNo, lineNo, stoppingToken);
                        _logger.LogInformation("Reconnected successfully.");
                    }
                }
                catch(MqttCommunicationException ex)
                {
                    _logger.LogWarning(ex, "MQTT Communication Exception for Floor:{floor}, Line:{line}. Retrying in 500ms...", floorNo, lineNo);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Reconnection failed from receiver.");
                    throw;
                }
            };

            await ConnectToMqttBroker(mqttClient, floorNo, lineNo, stoppingToken);

            while (!stoppingToken.IsCancellationRequested && mqttClient.IsConnected)
            {
                await Task.Delay(5000, stoppingToken);
            }
        }
        catch(MqttCommunicationException ex)
        {
            _logger.LogWarning(ex, "MQTT Communication Exception for Floor:{floor}, Line:{line}. Retrying in 500ms...", floorNo, lineNo);
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Task was canceled. Exiting gracefully...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT Client crashed for Floor:{floor}, Line:{line}. Restarting in 500ms...", floorNo, lineNo);
            if (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(500, stoppingToken);
            }
        }
    }

    private async Task MakeLongRunningMqttSenderClient(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Connecting to MQTT broker for sender");
            MqttFactory factory = new MqttFactory();
            IMqttClient? mqttClient = factory.CreateMqttClient();

            var mqttClientOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(_mqttConfig.Host, _mqttConfig.Port)
                .WithClientId($"line-server-sender")
                .WithCleanSession(false)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(15))
                .Build();

            mqttClient.DisconnectedAsync += async e =>
            {
                try
                {
                    await Task.Delay(500, stoppingToken);

                    await TryEstablishConnectionWithMqttBroker(mqttClient, mqttClientOptions, stoppingToken);

                    _logger.LogInformation("Reconnected sender client successfully.");
                }
                catch(MqttCommunicationException ex)
                {
                    _logger.LogWarning(ex, "MQTT Communication Exception for sender client. Retrying in 500ms...");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Reconnection failed from sender client.");
                }
            };

            await TryEstablishConnectionWithMqttBroker(mqttClient, mqttClientOptions, stoppingToken);

            _logger.LogInformation("MQTT sender client connected");

            while (await brokerMessageQueue.WaitForNextItemAsync(stoppingToken))
            {
                while (brokerMessageQueue.Count() > 0)
                {
                    PublishToBrokerRequest request = await brokerMessageQueue.DequeueAsync();
                    MqttApplicationMessage message = new MqttApplicationMessage
                    {
                        Topic = request.Topic,
                        PayloadSegment = new ArraySegment<byte>(Encoding.UTF8.GetBytes(request.Payload ?? "1")),
                        QualityOfServiceLevel = MqttQualityOfServiceLevel.ExactlyOnce,
                    };

                    while (!stoppingToken.IsCancellationRequested && !mqttClient.IsConnected)
                    {
                        await Task.Delay(5000, stoppingToken);
                    }

                    await mqttClient.PublishAsync(message, stoppingToken);
                }
            }
            
            _logger.LogWarning("Got out of loop..........");
        }
        catch(TaskCanceledException)
        {
            _logger.LogWarning("Task was canceled for sender. Exiting gracefully...");
        }
        catch(OperationCanceledException)
        {
            _logger.LogWarning("Operation canceled for sender. Exiting gracefully...");
        }
        catch(MqttCommunicationException ex)
        {
            _logger.LogWarning(ex, "MQTT Communication Exception for sender. Retrying in 500ms...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT Client crashed for sender. Restarting in 500ms...");
        }

    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        foreach (var floor in _floorConfig)
        {
            foreach (var line in floor.Lines)
            {
                _runningTasks.Add(MakeLongRunningMqttReceiverClient(floor.No, line.No, stoppingToken));
            }
        }

        _runningTasks.Add(MakeLongRunningMqttSenderClient(stoppingToken));

        await Task.WhenAny(_runningTasks);
    }
}