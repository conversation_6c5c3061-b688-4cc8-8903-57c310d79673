using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class LayoutRepository(ApplicationDbContext context) : ILayoutRepository
{
    public async Task<Layout?> GetActiveLayout()
    {
        return await context
            .Layouts
            .Include(l => l.LayoutItems)
            .FirstOrDefaultAsync(l => l.IsActive);
    }

    public async Task<IEnumerable<Layout>> GetAllLayouts()
    {
        return await context
            .Layouts
            .ToListAsync();
    }

    public async Task<Layout?> GetLayoutById(long layoutId)
    {
        return await context
            .Layouts
            .FirstOrDefaultAsync(l => l.Id == layoutId);
    }

    public async Task<Layout> ActivateLayout(Layout layout)
    {
        await context.Layouts.Where(l => l.IsActive).ExecuteUpdateAsync(s => s.SetProperty(l => l.IsActive, false));

        layout.IsActive = true;
        await context.SaveChangesAsync();

        return layout;
    }

    public async Task<LayoutItem?> GetActiveLayoutLayoutItemByStationId(long stationId)
    {
        Layout? layout = await context.Layouts.Where(l => l.IsActive).Select(l => new Layout { Id = l.Id, IsActive = l.IsActive }).FirstOrDefaultAsync();
        if (layout == null)
        {
            return null;
        }
        
        LayoutItem? layoutItem = await context.LayoutItems.Where(l => l.StationId == stationId && l.LayoutId == layout.Id).Select(l => new LayoutItem { LayoutId = l.LayoutId, StationId = l.StationId, Type = l.Type }).FirstOrDefaultAsync();
        
        return layoutItem;
    }

    public async Task<LayoutItem> GetNextLayoutItemOnMoveTaskNextStation(long stationId)
    {
        Layout activeLayout = await context
            .Layouts
            .Select(l => new Layout { Id = l.Id, IsActive = l.IsActive })
            .FirstAsync(l => l.IsActive);

        LayoutItem currentStationLayoutItem = await context.LayoutItems.FirstAsync(l => l.StationId == stationId && l.LayoutId == activeLayout.Id);

        return await context.LayoutItems
            .Where(l => l.Number == currentStationLayoutItem.Number + 1 && l.LayoutId == activeLayout.Id)
            .Include(l => l.Station)
            .Select(l => new LayoutItem { Id = l.Id, StationId = l.StationId, LayoutId = l.LayoutId, Number = l.Number, Station = l.Station })
            .FirstAsync();
    }

    public async Task<LayoutItem> GetQcLayoutItem(long layoutId)
    {
        return await context.LayoutItems.FirstAsync(l => l.LayoutId == layoutId && l.Station!.Type == StationTypeEnum.Qc);
    }
}
