using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class SwingRepository(ApplicationDbContext context, IStationModeRepository stationModeRepository): ISwingRepository
{
    public async Task<StationWorker?> GetLoggedStationWorkerByStationNumber(int stationNumber)
    {
        long stationId = await context.Stations.Where(s => s.Number == stationNumber).Select(s => s.Id).FirstAsync();
        return await context
            .StationWorkers
            .Where(s => s.StationId == stationId && EF.Functions.DateDiffDay(s.StartedAt.Date, DateTime.Today.Date) == 0 && s.EndedAt == null)
            .Select(s => new StationWorker { Id = s.Id, StationId = s.StationId, WorkerId = s.WorkerId, WorkerName = s.WorkerName })
            .FirstOrDefaultAsync();
    }
    
    public async Task<StationMode?> GetSwingStationModeByStationNumber(int stationNumber)
    {
        long stationId = await context.Stations.Where(s => s.Number == stationNumber).Select(s => s.Id).FirstAsync();
        return await stationModeRepository.GetCurrentByStation(stationId);
    }
}