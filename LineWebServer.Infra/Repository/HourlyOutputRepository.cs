using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class HourlyOutputRepository(ApplicationDbContext context): IHourlyOutputRepository
{
    public async Task CreateOnHourly()
    {
        // var now = DateTime.Now;
        // var startOfHour = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
        // var endOfHour = startOfHour.AddHours(1);
        //
        // long[] qcOrderIds = await context.Qcs
        //     .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //     .Select(q => q.OrderId)
        //     .Distinct()
        //     .ToArrayAsync();
        //
        // long[] qcAlterOrderIds = await context.QcAlters
        //     .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //     .Select(q => q.OrderId)
        //     .Distinct()
        //     .ToArrayAsync();
        //
        // long[] qcRejectOrderIds = await context.QcRejects
        //     .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //     .Select(q => q.OrderId)
        //     .Distinct()
        //     .ToArrayAsync();
        //
        // long[] orderIds = qcOrderIds.Concat(qcAlterOrderIds).Concat(qcRejectOrderIds).Distinct().ToArray();
        //
        // foreach (long targetOrderId in orderIds)
        // {
        //     int totalProductions = await context.Qcs
        //         .Where(q => q.OrderId == targetOrderId && q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //         .CountAsync();
        //
        //     int totalAlters = await context.QcAlters
        //         .Where(q => q.OrderId == targetOrderId && q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //         .CountAsync();
        //
        //     int totalRejects = await context.QcRejects
        //         .Where(q => q.OrderId == targetOrderId && q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
        //         .CountAsync();
        //     
        //     await context.HourlyOutputs.AddAsync(new HourlyOutput
        //     {
        //         OrderId = targetOrderId,
        //         Production = totalProductions,
        //         Alter = totalAlters,
        //         Reject = totalRejects
        //     });
        // }
        //
        //
        //
        
        
        
        
        
        var now = DateTime.Now;
        var startOfHour = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
        var endOfHour = startOfHour.AddHours(1);

        var hourlyOutputs = await context.Qcs
            .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
            .Select(q => new { q.OrderId, Type = "Production" })
            .Concat(
                context.QcAlters
                    .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
                    .Select(q => new { q.OrderId, Type = "Alter" })
            )
            .Concat(
                context.QcRejects
                    .Where(q => q.CreatedAt >= startOfHour && q.CreatedAt < endOfHour)
                    .Select(q => new { q.OrderId, Type = "Reject" })
            )
            .GroupBy(x => x.OrderId)
            .Select(g => new HourlyOutput
            {
                OrderId = g.Key,
                Production = g.Count(x => x.Type == "Production"),
                Alter = g.Count(x => x.Type == "Alter"),
                Reject = g.Count(x => x.Type == "Reject"),
            })
            .ToListAsync();

        await context.HourlyOutputs.AddRangeAsync(hourlyOutputs);
        await context.SaveChangesAsync();
        
    }
}
