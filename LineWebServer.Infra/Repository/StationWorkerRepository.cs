using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class StationWorkerRepository(ApplicationDbContext context): IStationWorkerRepository
{
    public async Task<StationWorker?> GetLoggedStationWorkerById(long stationId)
    {
        return await context
            .StationWorkers
            .Where(s => s.StationId == stationId && EF.Functions.DateDiffDay(s.StartedAt.Date, DateTime.Today.Date) == 0 && s.EndedAt == null)
            .FirstOrDefaultAsync();
    }
    
}