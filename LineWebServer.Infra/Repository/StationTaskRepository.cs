using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class StationTaskRepository(ApplicationDbContext context, ILayoutRepository layoutRepository) : IStationTaskRepository
{

    public async Task<StationTask?> GetCurrentTaskOfStation(long stationId)
    {
        return await context.StationTasks
            .Include(s => s.ItemAcquisition)
            .Where(s => s.StationId == stationId && s.Status == StationTaskStatusEnum.Processing)
            .Select(s => new StationTask
            {
                Id = s.Id,
                StationId = s.StationId,
                AmountType = s.AmountType,
                ItemQty = s.ItemQty,
                Status = s.Status,
                ItemAcquisition = s.ItemAcquisition,
                TotalProduction = s.TotalProduction
            })
            .FirstOrDefaultAsync();
    }

    public async Task MoveTaskNextStation(StationTask stationTask, LayoutItem nextLayoutItem)
    {
        if (nextLayoutItem.Station!.Type == StationTypeEnum.Qc)
        {
            await context
                .StationTasks
                .Where(s => s.Id == stationTask.Id).ExecuteUpdateAsync(s => s
                    .SetProperty(st => st.StationId, nextLayoutItem.StationId)
                    .SetProperty(st => st.TotalProduction, 0)
                    .SetProperty(st => st.Status, StationTaskStatusEnum.QualityChecking));

            return;
        }

        await context
            .StationTasks
            .Where(s => s.Id == stationTask.Id).ExecuteUpdateAsync(s => s
                .SetProperty(st => st.TotalProduction, 0)
                .SetProperty(st => st.StationId, nextLayoutItem.StationId));

    }


    public async Task HandleOverProductionOfTask(StationTask stationTask, LayoutItem nextLayoutItem, int overProductionQty)
    {
        long currentStationId = stationTask.StationId;
        await MoveTaskNextStation(stationTask, nextLayoutItem);

        await context.Database.ExecuteSqlRawAsync("""
                                                  UPDATE station_productions
                                                  SET StationId = {0}
                                                  WHERE TaskId = {1} AND StationId = {2}
                                                  ORDER BY CreatedAt DESC
                                                  LIMIT {3};
                                                  """, nextLayoutItem.StationId, stationTask.Id, currentStationId, overProductionQty);
       
        if (nextLayoutItem.Station!.Type == StationTypeEnum.Qc)
        {
            await context
                .StationTasks
                .Where(s => s.Id == stationTask.Id).ExecuteUpdateAsync(s => s
                    .SetProperty(st => st.StationId, nextLayoutItem.StationId)
                    .SetProperty(st => st.TotalProduction, overProductionQty)
                    .SetProperty(st => st.Status, StationTaskStatusEnum.Completed));

            return;
        }
        
        await context
            .StationTasks
            .Where(s => s.Id == stationTask.Id).ExecuteUpdateAsync(s => s
                .SetProperty(st => st.TotalProduction, overProductionQty)
                .SetProperty(st => st.StationId, nextLayoutItem.StationId)
            );
    }

    public async Task<StationTask> Create(StationTask stationTask)
    {
        await context.StationTasks.AddAsync(stationTask);

        return stationTask;
    }
}
