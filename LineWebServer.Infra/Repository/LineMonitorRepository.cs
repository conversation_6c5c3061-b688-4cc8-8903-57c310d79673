using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class LineMonitorRepository(ApplicationDbContext context) : ILineMonitorRepository
{
    public async Task<IEnumerable<Station>> GetAllStationsOrderedByNumber()
    {
        return await context.Stations
            .OrderBy(s => s.Number)
            .ToListAsync();
    }

    public async Task<IList<StationWorker>> GetCurrentDayActiveWorkers(DateTime today)
    {
        return await context.StationWorkers
            .Where(w => EF.Functions.DateDiffDay(w.StartedAt.Date, today) == 0 && w.EndedAt == null)
            .ToListAsync();
    }

    public async Task<int> GetWorkerProductionCount(string workerId, DateTime today)
    {
        return await context.StationProductions
            .Where(p =>
                p.WorkerId == workerId &&
                p.Type == ProductionTypeEnum.Production &&
                EF.Functions.DateDiffDay(p.CreatedAt.Date, today) == 0)
            .CountAsync();
    }

    public async Task<int> GetPendingTaskCount(long stationId)
    {
        return await context
            .StationTasks
            .Where(t => t.StationId == stationId && t.Status == StationTaskStatusEnum.Processing)
            .SumAsync(t => t.ItemQty);
    }

    public async Task<int> GetWorkerAlterCount(string workerId, DateTime today)
    {
        return await context.QcAlters
            .Where(a => a.WorkerId == workerId && 
                   EF.Functions.DateDiffDay(a.CreatedAt.Date, today) == 0)
            .CountAsync();
    }

    public async Task<int> GetWorkerRejectCount(string workerId, DateTime today)
    {
        return await context.QcRejects
            .Where(a => a.WorkerId == workerId && 
                   EF.Functions.DateDiffDay(a.CreatedAt.Date, today) == 0)
            .CountAsync();
    }

    public async Task<IEnumerable<StationWorker>> GetWorkerTimeDetails(string workerId, DateTime today)
    {
        return await context.StationWorkers
            .Where(w => w.WorkerId == workerId && 
                   EF.Functions.DateDiffDay(w.StartedAt.Date, today) == 0)
            .ToListAsync();
    }

    public async Task<Order?> GetCurrentActiveOrder(DateTime today)
    {
        return await context.Orders
            .Where(o => o.EndedAt == null && 
                   o.StartedAt != null && 
                   EF.Functions.DateDiffDay(o.StartedAt!.Value.Date, today) == 0)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<HourlyOutput>> GetHourlyStats(DateTime today)
    {
        return await context.HourlyOutputs
            .Include(h => h.Order)
            .Where(h => EF.Functions.DateDiffDay(h.CreatedAt.Date, today) == 0)
            .ToListAsync();
    }

    public async Task<IList<HourlyOutput>> GetHourlyStatsForDays(DateTime today, DateTime yesterday)
    {
        return await context.HourlyOutputs
            .Where(h => EF.Functions.DateDiffDay(h.CreatedAt.Date, today) == 0 || 
                       EF.Functions.DateDiffDay(h.CreatedAt.Date, yesterday) == 0)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<int> GetTotalWorkersCount()
    {
        return await context.StationWorkers
            .Select(w => w.WorkerId)
            .Distinct()
            .CountAsync();
    }

    public async Task<int> GetActiveWorkersCount()
    {
        return await context.StationWorkers
            .Where(w => w.EndedAt == null)
            .Select(w => w.WorkerId)
            .Distinct()
            .CountAsync();
    }

    public async Task<int> GetDailyProductionCount(DateTime today)
    {
        return await context.Qcs
            .Where(q =>  EF.Functions.DateDiffDay(q.CreatedAt.Date, today) == 0)
            .CountAsync();
    }

    public async Task<int> GetDailyAlterCount(DateTime today)
    {
        return await context.QcAlters.Where(q => EF.Functions.DateDiffDay(q.CreatedAt.Date, today) == 0).CountAsync();
    }

    public async Task<int> GetDailyRejectCount(DateTime today)
    {
        return await context.Qcs.Where(q => EF.Functions.DateDiffDay(q.CreatedAt.Date, today) == 0).CountAsync();
    }

    public async Task<IEnumerable<Station>> GetNonDefaultStations()
    {
        return await context.Stations
            .Where(s => s.Type != StationTypeEnum.Default)
            .ToListAsync();
    }

    public async Task<IEnumerable<StationWorker>> GetWorkersTimeForStations(IEnumerable<long> stationIds, DateTime today)
    {
        return await context.StationWorkers
            .Where(w => !stationIds.Contains(w.StationId) && 
                   EF.Functions.DateDiffDay(w.StartedAt.Date, today) == 0)
            .ToListAsync();
    }
}