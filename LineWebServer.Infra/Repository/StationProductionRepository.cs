using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class StationProductionRepository(ApplicationDbContext context): IStationProductionRepository
{
    public async Task Create(StationProduction stationProduction)
    {
        await context.StationProductions.AddAsync(stationProduction);

        if (stationProduction.Type == ProductionTypeEnum.Production)
        {
            await context.StationTasks.Where(s => s.Id == stationProduction.TaskId).ExecuteUpdateAsync(s => s.SetProperty(t => t.TotalProduction, t => t.TotalProduction + 1));
        }
    }    
}