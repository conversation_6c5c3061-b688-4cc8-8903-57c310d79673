using System.Collections;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class ItemAcquisitionRepository(ApplicationDbContext context) : IItemAcquisitionRepository
{
    public async Task<IEnumerable<ItemAcquisition>> Get(GetItemAcquisitionRequest dto)
    {
        var query = context.ItemAcquisitions.AsQueryable();

        if (dto.IsIdSet())
        {
            query = query.Where(r => r.Id == dto.Id);
        }

        if (dto.IsOrderIdSet())
        {
            query = query.Where(r => r.OrderId == dto.OrderId);
        }

        // TODO: Refactor this to reduce writing all fields one by one. maybe use spread operator somehow
        var result = await query.AsNoTracking().Select(i => new ItemAcquisition
        {
            Id = i.Id,
            OrgTransitionId = i.OrgTransitionId,
            OrderId = i.OrderId,
            SupervisorId = i.SupervisorId,
            AmountType = i.AmountType,
            Amount = i.Amount,
            BundleSize = i.BundleSize,
            CreatedAt = i.CreatedAt,
            TotalReleasedQty = context.ItemReleases.Where(r => r.ItemAcquisitionId == i.Id).Sum(r => r.ItemQty)
        }).ToListAsync();

        return result;
    }

    public async Task<ItemAcquisition> CreateAsync(ItemAcquisition itemAcquisition)
    {
        await context.ItemAcquisitions.AddAsync(itemAcquisition);

        return itemAcquisition;
    }

    public ItemAcquisition Update(ItemAcquisition itemAcquisition)
    {
        context.ItemAcquisitions.Update(itemAcquisition);

        return itemAcquisition;
    }

    public async Task<Order?> GetOrderByOrderId(string orderId, DateTime targetDate)
    {
        return await context
            .Orders
            .Select(o => new Order { Id = o.Id, OrderId = o.OrderId, AssignedAt = o.AssignedAt })
            .FirstOrDefaultAsync(o =>
                o.OrderId == orderId
                && EF.Functions.DateDiffDay(o.AssignedAt.Date, targetDate.Date) == 0);

    }

    public async Task<Layout> GetActiveLayout()
    {
        return await context.Layouts.FirstAsync(l => l.IsActive);
    }

    public async Task<LayoutItem> GetFirstItemOfLayout(long layoutId)
    {
        return await context
            .LayoutItems
            .Select(l => new LayoutItem { Id = l.Id, StationId = l.StationId, LayoutId = l.LayoutId, Number = l.Number })
            .FirstAsync(l => l.LayoutId == layoutId && l.Number == 1);
    }

    public async Task<int> ReleaseItemAcquistion(ReleaseItemAcquisitionRequest requestDto)
    {
        Layout layout = await GetActiveLayout();
        LayoutItem layoutItem = await GetFirstItemOfLayout(layout.Id);
        
        ItemAcquisition itemAcquisition = await context.ItemAcquisitions.Select(i => new ItemAcquisition
        {
            Id = i.Id,
            AmountType = i.AmountType,
            BundleSize = i.BundleSize,
            OrderId = i.OrderId,
        }).FirstAsync(r => r.Id == requestDto.ItemAcquisitionId);

        IList<ItemRelease> itemReleases = [];

        if (itemAcquisition.AmountType == ItemAcquisitionTypeEnum.Bundle)
        {
            for (int i = 1; i <= requestDto.AmountToBeReleased; i++)
            {
                itemReleases.Add(new ItemRelease
                {
                    ItemAcquisitionId = requestDto.ItemAcquisitionId,
                    AmountType = itemAcquisition.AmountType,
                    ItemQty = 1,
                });
            }
        }
        else
        {
            itemReleases.Add(new ItemRelease
            {
                ItemAcquisitionId = requestDto.ItemAcquisitionId,
                AmountType = itemAcquisition.AmountType,
                ItemQty = requestDto.AmountToBeReleased,
            });
        }

        foreach (ItemRelease item in itemReleases)
        {
            await context.ItemReleases.AddAsync(item);

            if (item.AmountType == ItemAcquisitionTypeEnum.Bundle)
            {
                await context.StationTasks.AddAsync(new StationTask
                {
                    ItemAcquisitionId = requestDto.ItemAcquisitionId,
                    StationId = layoutItem.StationId,
                    AmountType = item.AmountType,
                    ItemQty = itemAcquisition.BundleSize,
                    ItemRelease = item,
                    OrderId = itemAcquisition.OrderId,
                });

                continue;
            }

            await context.StationTasks.AddAsync(new StationTask
            {
                ItemAcquisitionId = requestDto.ItemAcquisitionId,
                StationId = layoutItem.StationId,
                AmountType = item.AmountType,
                ItemQty = item.ItemQty,
                ItemRelease = item,
                OrderId = itemAcquisition.OrderId,
            });
        }

        Station station = await context
            .Stations
            .Select(s => new Station { Id = s.Id, Number = s.Number })
            .FirstAsync(s => s.Id == layoutItem.StationId);

        return station.Number;
    }

    public async Task<bool> IsExist(RackExchangeRepositoryIsExistDto dto)
    {
        var query = context.ItemAcquisitions.AsQueryable();

        if (dto.IsIdSet())
        {
            query = query.Where(r => r.Id == dto.Id);
        }

        return await query.AnyAsync();
    }

    public async Task<int> GetReleasableQtyFromAcquisition(long itemAcquisitionId)
    {
        var itemAcquisition = await context
            .ItemAcquisitions
            .Select(i => new ItemAcquisition { Id = i.Id, Amount = i.Amount })
            .FirstAsync(r => r.Id == itemAcquisitionId);

        int alreadyInlineAmount = await context.ItemReleases
            .Where(r => r.ItemAcquisitionId == itemAcquisition.Id)
            .SumAsync(r => r.ItemQty);

        return itemAcquisition.Amount - alreadyInlineAmount;
    }
}
