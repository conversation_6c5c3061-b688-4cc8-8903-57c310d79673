using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class StationModeRepository(ApplicationDbContext context): IStationModeRepository
{
    public async Task<StationMode?> GetCurrentByStation(long stationId)
    {
        return await context.StationModes.Where(s => s.StationId == stationId && s.EndedAt == null).FirstOrDefaultAsync();
    }

    public async Task Create(StationMode stationMode)
    {
        await context.StationModes.AddAsync(stationMode);
    }

    public void Update(StationMode stationMode)
    {
        context.StationModes.Update(stationMode);
    }
}