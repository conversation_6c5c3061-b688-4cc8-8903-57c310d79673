using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class EventRepository(ApplicationDbContext context, IUnitOfWork unitOfWork) : IEventRepository
{
    public async Task Create(Event @event)
    {
        await context.Events.AddAsync(@event);
    }
    
    public async Task HandlePreviouslyWorkerNotLoggedOutScenario(Event @event)
    {
        var previousNotLoggedOutEntries = await context
            .StationWorkers
            .Where(s => s.WorkerId == @event.Payload 
                        && s.EndedAt == null 
                        && s.StartedAt.Date < DateTime.Today.Date)
            .Select(s => new { s.Id, s.StartedAt, s.EndedAt })
            .AsNoTracking()
            .ToListAsync();
            
        foreach (var entry in previousNotLoggedOutEntries)
        {
            var latestProductionEntry = await context
                .StationProductions
                .Where(p => p.WorkerId == @event.Payload && EF.Functions.DateDiffDay(p.CreatedAt.Date, entry.StartedAt.Date) == 0)
                .OrderByDescending(p => p.CreatedAt)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            var endedAt = latestProductionEntry?.CreatedAt ?? entry.StartedAt;
                
            await context.StationWorkers.Where(s => s.Id == entry.Id).Take(1).ExecuteUpdateAsync(s => s.SetProperty(sw => sw.EndedAt, endedAt));
        }
    }
    
    public async Task HandleIfWorkerCurrentlyLoggedInAnotherStation(Event @event, Station station)
    {
        await context
            .StationWorkers
            .Where(sw => sw.WorkerId == @event.Payload
                         && station.Id != sw.StationId
                         && EF.Functions.DateDiffDay(DateTime.Today.Date, sw.StartedAt.Date) == 0
                         && sw.EndedAt == null)
            .ExecuteUpdateAsync(s => s.SetProperty(sw => sw.EndedAt, DateTime.Now));
    }
    
    public async Task<StationWorker?> GetCurrentStationWorker(Event @event, Station station)
    {
        return await context
            .StationWorkers
            .Where(s => s.WorkerId == @event.Payload  
                        && station.Id == s.StationId
                        && EF.Functions.DateDiffDay(s.StartedAt.Date, DateTime.Today.Date) == 0
                        && s.EndedAt == null)
            .FirstOrDefaultAsync();
    }
    
    public async Task<StationWorker> HandleCurrentStationLogIn(Event @event, Station station, string workerName, StationWorker? loggedInStationWorker)
    {
        if (loggedInStationWorker != null)
        {
            loggedInStationWorker.EndedAt = DateTime.Now;
            await unitOfWork.SaveChangesAsync();
            return loggedInStationWorker;
        }

        loggedInStationWorker = new StationWorker
        {
            StationId = station.Id,
            WorkerId = @event.Payload!,
            StartedAt = DateTime.Now,
            WorkerName = workerName
        };
        await context.StationWorkers.AddAsync(loggedInStationWorker);
        await unitOfWork.SaveChangesAsync();

        return loggedInStationWorker;
    }
    
}