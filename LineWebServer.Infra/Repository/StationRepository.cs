using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class StationRepository(ApplicationDbContext context): IStationRepository
{
    public async Task<Station?> GetStationByNumber(int stationNumber)
    {
        return await context.Stations.Where(s => s.Number == stationNumber).FirstOrDefaultAsync();
    }

    public void Update(Station station)
    {
        context.Stations.Update(station);
    }

}