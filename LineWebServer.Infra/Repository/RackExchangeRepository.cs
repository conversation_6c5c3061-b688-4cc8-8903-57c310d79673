using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Cutting.DTOs;
using LineWebServer.Application.Cutting.DTOs.Repositories;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class RackExchangeRepository(ApplicationDbContext context): IRackExchangeRepository
{
    public async Task<IEnumerable<RackExchange>> Get(GetRackExchangesRequest dto)
    {
        var query = context.RackExchanges.AsQueryable();

        if (dto.IsIdSet())
        {
            query = query.Where(r => r.Id == dto.Id);
        }
        if (dto.IsOrderIdSet())
        {
            query = query.Where(r => r.OrderId == dto.OrderId);
        }
        if (dto.IsStatusSet())
        {
            query = query.Where(r => dto.Status.Contains(r.Status));
        }
        if (dto.IsParentIdSet())
        {
            query = query.Where(r => r.ParentId == dto.ParentId);
        }
        
        var result = await query.Select(r => 
            new RackExchange
            {
                Id = r.Id,
                OrderId = r.OrderId,
                Status = r.Status,
                ParentId = r.ParentId,
                Amount = r.Amount,
                TotalAmount = context.RackExchanges
                    .Where(child => child.ParentId == r.Id)
                    .Sum(child => child.Amount)
            }).AsNoTracking().ToListAsync();

        return result;
    }

    public async Task<RackExchange> CreateAsync(RackExchange rackExchange)
    {
        await context.RackExchanges.AddAsync(rackExchange);

        return rackExchange;
    }

    public RackExchange Update(RackExchange rackExchange)
    {
        context.RackExchanges.Update(rackExchange);
        
        return rackExchange;
    }

    public async Task<Order?> GetOrderByOrderId(string orderId, DateTime targetDate)
    {
        return await context
            .Orders
            .Select(o => new Order{ Id = o.Id, OrderId = o.OrderId, AssignedAt = o.AssignedAt })
            .FirstOrDefaultAsync(o => 
                o.OrderId == orderId 
                && EF.Functions.DateDiffDay(o.AssignedAt.Date, targetDate.Date) == 0);
        
    }

    public async Task<Layout> GetActiveLayout()
    {
        return await context.Layouts.FirstAsync(l => l.IsActive);
    }

    public async Task<LayoutItem> GetFirstItemOfLayout(long layoutId)
    {
        return await context
            .LayoutItems
            .Select(l => new LayoutItem { Id = l.Id, StationId = l.StationId, LayoutId = l.LayoutId, Number = l.Number })
            .FirstAsync(l => l.LayoutId == layoutId && l.Number == 1);
    }
    
    public async Task<int> DeliveryFromRack(DeliverBundleFromRackRequest requestDto)
    {
        Layout layout =  await GetActiveLayout();
        LayoutItem layoutItem = await GetFirstItemOfLayout(layout.Id);

        int totalDelivered = await context.RackExchanges
            .Where(r => r.ParentId == requestDto.RackExchangeId)
            .SumAsync(r => r.Amount) + requestDto.Amount;

        RackExchange rackExchange = await context.RackExchanges.FirstAsync(r => r.Id == requestDto.RackExchangeId);
        rackExchange.Status = rackExchange.Amount == totalDelivered ? LineRackExchangeStatusEnum.Delivered : LineRackExchangeStatusEnum.PartialDelivered;

        RackExchange newRackExchange = new RackExchange
        {
            OrderId = rackExchange.OrderId,
            SupervisorId = rackExchange.SupervisorId,
            ExchangeType = RackExchangeTypeEnum.Out,
            AmountType = rackExchange.AmountType,
            Status = LineRackExchangeStatusEnum.InLine,
            Amount = requestDto.Amount,
            ParentId = rackExchange.Id,
            OrgRackExchangeId = rackExchange.OrgRackExchangeId,
        };
        await context.RackExchanges.AddAsync(newRackExchange);
        
        StationTask stationTask = new StationTask
        {
            StationId = layoutItem.StationId,
            AmountType = newRackExchange.AmountType,
            Amount = requestDto.Amount,
        };
        await context.StationTasks.AddAsync(stationTask);
        
        var station = await context
            .Stations
            .Select(s => new { s.Id, s.Number })
            .FirstAsync(s => s.Id == layoutItem.StationId);
        
        return station.Number;
    }
    
    public async Task<bool> IsExist(RackExchangeRepositoryIsExistDto dto)
    {
        var query = context.RackExchanges.AsQueryable();
        if (dto.IsIdSet())
        {
            query = query.Where(r => r.Id == dto.Id);
        }

        return await query.AnyAsync();
    }

    public async Task<int> GetAmountDeliverableToLine(long rackExchangeId)
    {
        var parentRackExchange = await context.RackExchanges.FirstAsync(r => r.Id == rackExchangeId);
        int alreadyInlineAmount = await context.RackExchanges
            .Where(r => r.ParentId == parentRackExchange.Id)
            .SumAsync(r => r.Amount);
        return parentRackExchange.Amount - alreadyInlineAmount;
    }
}
