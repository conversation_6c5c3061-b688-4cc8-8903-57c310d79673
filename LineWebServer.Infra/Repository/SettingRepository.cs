using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class SettingRepository(ApplicationDbContext context): ISettingRepository
{
    public async Task<Setting?> GetAsync(string key)
    {
        var setting = await context.Settings.FirstOrDefaultAsync(s => s.Key == key);

        return setting;
    }

    public async Task<Setting> PutAsync(string key, string? value)
    {
        var setting = new Setting
        {
            Key = key,
            Value = value ?? ""
        };
        
        Setting? existedSetting = await GetAsync(key);

        if (existedSetting != null)
        {
            existedSetting.Value = value ?? "";
            return existedSetting;
        }
        
        await context.Settings.AddAsync(setting);

        return setting;
    }
}