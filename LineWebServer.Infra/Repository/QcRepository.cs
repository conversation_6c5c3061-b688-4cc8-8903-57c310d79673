using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class QcRepository(ApplicationDbContext context) : IQcRepository
{
    public async Task<Qc> CreateQc(long orderId, long taskId, bool isCompleted = false)
    {
        var qc = new Qc
        {
            OrderId = orderId,
            TaskId = taskId,
        };
        
        await context.Qcs.AddAsync(qc);

        if (isCompleted)
        {
            await context.StationTasks.Where(s => s.Id == taskId).ExecuteUpdateAsync(s => s
                .SetProperty(t => t.Status, StationTaskStatusEnum.Completed)
                .SetProperty(t => t.TotalProduction, t => t.TotalProduction + 1)
            );
            
            return qc;
        }
        
        await context.StationTasks.Where(s => s.Id == taskId).ExecuteUpdateAsync(s => s.SetProperty(t => t.TotalProduction, t => t.TotalProduction + 1));

        return qc;
    }

    public async Task<Layout> GetActiveLayout()
    {
        return await context.Layouts.FirstAsync(l => l.IsActive);
    }

    public async Task<LayoutItem> GetLayoutItem(long layoutId, long stationId)
    {
        return await context.LayoutItems.FirstAsync(l => l.LayoutId == layoutId && l.StationId == stationId);
    }

    public async Task<StationWorker?> GetCurrentStationWorker(long stationId)
    {
        return await context.StationWorkers
            .FirstOrDefaultAsync(w => w.StationId == stationId && w.EndedAt == null);
    }

    public async Task<StationWorker?> GetTodayLastStationWorker(long stationId)
    {
        return await context.StationWorkers
            .FirstOrDefaultAsync(w => 
                w.StationId == stationId && 
                EF.Functions.DateDiffDay(w.StartedAt.Date, DateTime.Today.Date) == 0 && 
                w.EndedAt != null);
    }

    public void CreateQcAlter(QcAlter qcAlter)
    {
        context.QcAlters.Add(qcAlter);
    }

    public void CreateQcReject(QcReject qcReject)
    {
        context.QcRejects.Add(qcReject);
    }

    public async Task<bool> OrderExists(long orderId)
    {
        return await context.Orders.AnyAsync(o => o.Id == orderId && o.StartedAt != null);
    }

    public async Task<bool> StationExists(long stationId)
    {
        return await context.Stations.AnyAsync(s => s.Id == stationId);
    }

    public async Task<IList<Order>> GetActiveOrders()
    {
        return await context.Orders.Where(o => o.StartedAt != null && o.EndedAt == null).AsNoTracking().ToListAsync();
    }

    public async Task<Order?> GetActiveOrderDetails(long orderId)
    {
        return await context.Orders.AsNoTracking().Select(o => new Order
        {
            Id = o.Id,
            OrderId = o.OrderId,
            Label = o.Label,
            BuyerName = o.BuyerName,
            Country = o.Country,
            OrderQuantity = o.OrderQuantity,
            HourlyTarget = o.HourlyTarget,
            AssignedAt = o.AssignedAt,
            StartedAt = o.StartedAt,
            EndedAt = o.EndedAt,
            TotalProductions = context.Qcs.Count(q => q.OrderId == o.Id),
            TotalAlters = context.QcAlters.Count(q => q.OrderId == o.Id),
            TotalRejects = context.QcRejects.Count(q => q.OrderId == o.Id),
        }).FirstOrDefaultAsync(o => o.Id == orderId);
    }

    public async Task<Order?> GetOrderStat(long orderId)
    {
        return await context.Orders.AsNoTracking().Select(o => new Order
        {
            Id = o.Id,
            TotalProductions = context.Qcs.Count(q => q.OrderId == o.Id),
            TotalAlters = context.QcAlters.Count(q => q.OrderId == o.Id),
            TotalRejects = context.QcRejects.Count(q => q.OrderId == o.Id),
        }).FirstOrDefaultAsync(o => o.Id == orderId);
    }
}