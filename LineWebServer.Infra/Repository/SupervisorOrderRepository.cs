using System.Collections;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Repository;
using LineWebServer.Application.Supervisors.Enums;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.Repository;

public class SupervisorOrderRepository(ApplicationDbContext context) : ISupervisorOrderRepository
{
    public async Task<IEnumerable<Order>> GetAllAsync(GetAllOrdersRequest ordersRequest, IEnumerable<OrderExpandableEnum>? expands = null)
    {
        IQueryable<Order> query = context.Orders.AsQueryable();

        if (ordersRequest.IsIdSet())
            query = query.Where(o => o.Id == ordersRequest.Id);

        if (ordersRequest.IsLabelSet())
            query = query.Where(o => o.Label != null && ordersRequest.Label != null && o.Label.Contains(ordersRequest.Label));

        if (ordersRequest.IsBuyerNameSet())
            query = query.Where(o => ordersRequest.BuyerName != null && o.BuyerName.Contains(ordersRequest.BuyerName));

        if (ordersRequest.IsCountrySet())
            query = query.Where(o => o.Country != null && ordersRequest.Country != null && o.Country.Contains(ordersRequest.Country));

        if (ordersRequest.IsOrderQuantitySet())
            query = query.Where(o => o.OrderQuantity == ordersRequest.OrderQuantity);

        if (ordersRequest.IsHourlyTargetSet())
            query = query.Where(o => o.HourlyTarget == ordersRequest.HourlyTarget);

        if (ordersRequest.IsAssignedAtSet())
            query = query.Where(o => EF.Functions.DateDiffDay(o.AssignedAt.Date, ordersRequest.AssignedAt) == 0);

        if (ordersRequest.IsStartedAtSet())
            query = query.Where(o => o.StartedAt != null && EF.Functions.DateDiffDay(o.StartedAt!.Value.Date, ordersRequest.StartedAt) == 0);

        if (ordersRequest.IsEndedAtSet())
            query = query.Where(o => o.EndedAt != null && EF.Functions.DateDiffDay(o.EndedAt!.Value.Date, ordersRequest.EndedAt) == 0);

        if (ordersRequest.IsExpandsSet() && ordersRequest.Expands!.Any())
        {
            foreach (var expand in ordersRequest.Expands!)
            {
                if (expand == OrderExpandableEnum.HourlyOutputs.Name)
                    query = query.Include(o => o.HourlyOutputs);
                else if (expand == OrderExpandableEnum.Qcs.Name)
                    query = query.Include(o => o.Qcs);
                else if (expand == OrderExpandableEnum.QcAlters.Name)
                    query = query.Include(o => o.QcAlters);
                else if (expand == OrderExpandableEnum.QcRejects.Name)
                    query = query.Include(o => o.QcRejects);
                else if (expand == OrderExpandableEnum.RackExchanges.Name)
                    query = query.Include(o => o.ItemAcquisitions);
            }
        }

        return await query.ToListAsync();
    }

    
    public async Task<Order?> GetOrderById(long orderId, IEnumerable<string>? expands = null)
    {
        IQueryable<Order> query = context.Orders.AsQueryable();

        if (expands == null)
        {
            return await query.FirstOrDefaultAsync(o => o.Id == orderId);
        }

        foreach (var expand in expands)
        {
            if (expand == OrderExpandableEnum.HourlyOutputs.Name)
            {
                query = query.Include(o => o.HourlyOutputs);  
            }
            else if (expand == OrderExpandableEnum.Qcs.Name)
            {
                query = query.Include(o => o.Qcs);  
            }
            else if (expand == OrderExpandableEnum.QcAlters.Name)
            {
                query = query.Include(o => o.QcAlters);  
            }
            else if (expand == OrderExpandableEnum.QcRejects.Name)
            {
                query = query.Include(o => o.QcRejects);  
            }
            else if (expand == OrderExpandableEnum.RackExchanges.Name)
            {
                query = query.Include(o => o.ItemAcquisitions);  
            }
        }

        return await query.FirstOrDefaultAsync(o => o.Id == orderId);
    }
    
    public async Task<Order?> GetActiveOrder()
    {
        return await context.Orders.FirstOrDefaultAsync(o => o.StartedAt != null && o.EndedAt == null);
    }

    public async Task<Order?> GetOrderActivity(long orderId)
    {
        IEnumerable<Layout> layouts = await context.Layouts.ToListAsync();
        
    }

    public async Task<bool> IsExist(IsOrderExistDto dto)
    {
        var query = context.Orders.AsQueryable();
        if (dto.IsIdSet())
        {
            query = query.Where(o => o.Id == dto.Id);
        }
        if (dto.IsOrderStarted() && dto.OrderStarted)
        {
            query = query.Where(o => o.StartedAt != null && o.EndedAt == null);
        }
        if (dto.IsOrderStarted() && !dto.OrderStarted)
        {
            query = query.Where(o => o.StartedAt == null && o.EndedAt == null);
        }
        if (dto.IsOrderEndedSet() && dto.OrderEnded)
        {
            query = query.Where(o => o.StartedAt != null && o.EndedAt != null);
        }
        if (dto.IsOrderEndedSet() && !dto.OrderEnded)
        {
            query = query.Where(o => o.StartedAt != null && o.EndedAt == null);
        }
        if (dto.IsOrderIdSet())
        {
            query = query.Where(o => o.OrderId == dto.OrderId);
        }
        return await query.AnyAsync();
    }

    public void Update(Order order)
    {
        context.Orders.Update(order);
    }

    public async Task StartOrder(long orderId, DateTime startedAt)
    {
        await context.Orders.Where(o => o.Id == orderId).ExecuteUpdateAsync(s => s.SetProperty(o => o.StartedAt, startedAt));
    }
    public async Task EndOrder(long orderId, DateTime endedAt)
    {
        await context.Orders.Where(o => o.Id == orderId).ExecuteUpdateAsync(s => s.SetProperty(o => o.EndedAt, endedAt));
    }
    
    public async Task Create(Order order)
    {
        await context.Orders.AddAsync(order);
    }
}
