<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.SmartEnum" Version="8.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.14" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.2" />
        <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />

        <PackageReference Include="MQTTnet" Version="4.3.3.952" />
        <PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.3.952" />

        
    </ItemGroup>


    <ItemGroup>
      <ProjectReference Include="..\Domain\Domain.csproj" />
      <ProjectReference Include="..\LineWebServer.Application\LineWebServer.Application.csproj" />
    </ItemGroup>


    <ItemGroup>
      <Folder Include="Filters\" />
    </ItemGroup>

</Project>
