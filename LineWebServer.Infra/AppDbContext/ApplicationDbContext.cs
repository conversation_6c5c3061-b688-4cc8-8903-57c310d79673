using Domain.Entities;
using Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Infra.AppDbContext;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }
    
    public DbSet<Event> Events { get; set; }
    public DbSet<Station> Stations { get; set; }
    public DbSet<StationMode> StationModes { get; set; }
    public DbSet<StationProduction> StationProductions { get; set; }
    public DbSet<StationWorker> StationWorkers { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<HourlyOutput> HourlyOutputs { get; set; }
    
    public DbSet<ItemAcquisition> ItemAcquisitions { get; set; }
    public DbSet<Qc> Qcs { get; set; }
    public DbSet<QcAlter> QcAlters { get; set; }
    public DbSet<QcReject> QcRejects { get; set; }
    public DbSet<Layout> Layouts { get; set; }
    public DbSet<LayoutItem> LayoutItems { get; set; }
    public DbSet<Setting> Settings { get; set; }
    public DbSet<StationTask> StationTasks { get; set; }
    public DbSet<ItemRelease> ItemReleases { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Event>().ToTable("event");
        modelBuilder.Entity<Station>().ToTable("stations");
        modelBuilder.Entity<StationMode>().ToTable("station_modes");
        
        modelBuilder.Entity<StationProduction>()
            .ToTable("station_productions")
            .Property(s => s.StationType)
            .HasDefaultValue(StationTypeEnum.Default);
        modelBuilder.Entity<StationProduction>()
            .HasOne<StationTask>()
            .WithMany()
            .HasForeignKey(s => s.TaskId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<StationWorker>().ToTable("station_workers");
        
        modelBuilder.Entity<Order>().ToTable("orders");
        modelBuilder.Entity<Order>().Ignore("TotalProductions");
        modelBuilder.Entity<Order>().Ignore("TotalAlters");
        modelBuilder.Entity<Order>().Ignore("TotalRejects");

        modelBuilder.Entity<HourlyOutput>().ToTable("hourly_outputs");
        modelBuilder.Entity<HourlyOutput>()
            .HasOne<Order>(h => h.Order)
            .WithMany(o => o.HourlyOutputs)
            .HasForeignKey(h => h.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<ItemAcquisition>()
            .ToTable("item_acquisitions");
        modelBuilder.Entity<ItemAcquisition>()
            .HasOne<Order>(q => q.Order)
            .WithMany(o => o.ItemAcquisitions)
            .HasForeignKey(q => q.OrderId)
            .OnDelete(DeleteBehavior.Cascade);
        modelBuilder.Entity<ItemAcquisition>().Ignore(i => i.TotalReleasedQty);

        modelBuilder.Entity<ItemRelease>()
            .ToTable("item_releases");
        modelBuilder.Entity<ItemRelease>()
            .HasOne<ItemAcquisition>(r => r.ItemAcquisition)
            .WithMany()
            .HasForeignKey(r => r.ItemAcquisitionId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<Qc>().ToTable("qcs");
        modelBuilder.Entity<Qc>()
            .HasOne<Order>(q => q.Order)
            .WithMany(o => o.Qcs)
            .HasForeignKey(q => q.OrderId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<QcAlter>().ToTable("qc_alters");
        modelBuilder.Entity<QcAlter>()
            .HasOne<Order>(q => q.Order)
            .WithMany(o => o.QcAlters)
            .HasForeignKey(q => q.OrderId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<QcReject>().ToTable("qc_rejects");
        modelBuilder.Entity<QcReject>()
            .HasOne<Order>(q => q.Order)
            .WithMany(o => o.QcRejects)
            .HasForeignKey(q => q.OrderId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<Layout>().ToTable("layouts");
        modelBuilder.Entity<Layout>()
            .Property(q => q.IsActive)
            .HasDefaultValue(false);
        
        modelBuilder.Entity<LayoutItem>().ToTable("layout_items");
        modelBuilder.Entity<LayoutItem>()
            .HasOne<Layout>(q => q.Layout)
            .WithMany(l => l.LayoutItems)
            .HasForeignKey(l => l.LayoutId)
            .OnDelete(DeleteBehavior.Cascade);
        
        modelBuilder.Entity<Setting>().ToTable("settings");
        
        modelBuilder.Entity<StationTask>().ToTable("station_tasks");
        modelBuilder.Entity<StationTask>()
            .HasOne(s => s.Station)
            .WithMany(r => r.StationTasks)
            .HasForeignKey(st => st.StationId)
            .HasConstraintName("FK_StationTask_Station_Custom")
            .OnDelete(DeleteBehavior.Cascade);
        modelBuilder.Entity<StationTask>()
            .HasOne(s => s.ItemRelease)
            .WithMany(r => r.StationTasks)
            .HasForeignKey(re => re.ItemReleaseId)
            .HasConstraintName("FK_StationTask_ItemRelease_Custom")
            .OnDelete(DeleteBehavior.Cascade);
        modelBuilder.Entity<StationTask>()
            .HasOne(r => r.ItemAcquisition)
            .WithMany(r => r.StationTasks)
            .HasForeignKey(r => r.ItemAcquisitionId)
            .HasConstraintName("FK_StationTask_ItemAcquisition_Custom")
            .OnDelete(DeleteBehavior.Cascade);
    }
}