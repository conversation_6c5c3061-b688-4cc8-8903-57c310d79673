using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class RackExchange
{
    private int _totalAmount;
    private Order? _order;
    private RackExchange? _parent;
    private IEnumerable<RackExchange>? _children;
    
    private bool _isTotalAmountSet;
    private bool _isOrderSet;
    private bool _isParentSet;
    private bool _isChildrenSet;

    [Key] public long Id { get; set; }
    public long OrderId { get; set; }
    public long OrgRackExchangeId { get; set; }
    [Required, StringLength(50)] public string SupervisorId { get; set; } = null!;
    [Required, StringLength(30)] public string ExchangeType { get; set; } = RackExchangeTypeEnum.In;
    [Required, StringLength(30)] public string AmountType { get; set; } = RackExchangeAmountTypeEnum.Bundle;
    [Required, StringLength(30)] public string Status { get; set; } = LineRackExchangeStatusEnum.InRack;
    public long? ParentId { get; set; } = null;
    public int Amount { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;

    public Order? Order
    {
        get => _order;
        set
        {
            _order = value;
            _isOrderSet = true;
        }
    }

    public RackExchange? Parent
    {
        get => _parent;
        set
        {
            _parent = value;
            _isParentSet = true;
        }
    }

    public IEnumerable<RackExchange>? Children
    {
        get => _children;
        set
        {
            _children = value;
            _isChildrenSet = true;
        }
    }
    
    public int TotalAmount
    {
        get => _totalAmount;
        set
        {
            _totalAmount = value;
            _isTotalAmountSet = true;
        }
    }
    public bool IsTotalAmountSet() => _isTotalAmountSet;
    public bool IsOrderSet() => _isOrderSet;
    public bool IsParentSet() => _isParentSet;
    public bool IsChildrenSet() => _isChildrenSet;
}
