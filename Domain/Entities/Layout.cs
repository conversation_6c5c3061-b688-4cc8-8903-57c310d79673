using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities;

public class Layout
{
    [Key] public long Id { get; set; }
    [Required, StringLength(50)] public string Label { get; set; } = null!;
    public bool IsActive { get; set; } = false;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
    public IEnumerable<LayoutItem>? LayoutItems { get; set; }
}
