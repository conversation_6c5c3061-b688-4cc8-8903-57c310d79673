using System.ComponentModel.DataAnnotations;

namespace Domain.Entities;

public class Order
{
    [Key] public long Id { get; set; }
    [StringLength(50)] public string? Label { get; set; }
    [Required, StringLength(50)] public string BuyerName { get; set; } = null!;
    [Required, StringLength(50)] public string OrderId { get; set; } = null!;
    [StringLength(50)] public string? Country { get; set; }
    [Required] public int OrderQuantity { get; set; }
    [Required] public int HourlyTarget { get; set; }
    [Required] public DateTime AssignedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }

    public IEnumerable<HourlyOutput>? HourlyOutputs { get; set; }
    public IEnumerable<Qc>? Qcs { get; set; }
    public IEnumerable<QcAlter>? QcAlters { get; set; }
    public IEnumerable<QcReject>? QcRejects { get; set; }
    public IEnumerable<ItemAcquisition>? ItemAcquisitions { get; set; }

    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}
