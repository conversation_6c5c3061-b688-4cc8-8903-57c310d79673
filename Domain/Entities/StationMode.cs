using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class StationMode
{
    [Key] public long Id { get; set; }
    [Required] public long StationId { get; set; }
    [Required, StringLength(30)] public string Mode { get; set; } = StationModeEnum.Production;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime StartedAt { get; set; } = DateTime.Now;
    public DateTime? EndedAt { get; set; }
}
