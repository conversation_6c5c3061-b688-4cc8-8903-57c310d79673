using System.ComponentModel.DataAnnotations;

namespace Domain.Entities;

public class LayoutItem
{
    [Key] public long Id { get; set; }
    public long LayoutId { get; set; }
    public long StationId { get; set; }
    [Required] public int Number { get; set; }
    [Required, StringLength(50)] public string Type { get; set; } = null!;
    public Layout? Layout { get; set; }
    public Station? Station { get; set; }
}
