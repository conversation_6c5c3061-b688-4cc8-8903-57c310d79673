using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class ItemRelease
{
    [Key] public long Id { get; set; }
    [Required] public long ItemAcquisitionId { get; set; }
    [Required] public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    [Required] public int ItemQty { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;

    public ItemAcquisition? ItemAcquisition { get; set; }
    public List<StationTask>? StationTasks { get; set; }
}
