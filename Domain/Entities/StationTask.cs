using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class StationTask
{
    [Key] public long Id { get; set; }
    [Required] public long StationId { get; set; }
    [Required] public long ItemAcquisitionId { get; set; }
    [Required] public long ItemReleaseId { get; set; }
    [Required] public long OrderId { get; set; }
    [Required, StringLength(50)] public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    [Required] public int ItemQty { get; set; }
    [Required] public string Status { get; set; } = StationTaskStatusEnum.Processing;
    public int TotalProduction { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime? CreatedAt { get; set; } = DateTime.Now;

    public Station? Station { get; set; }
    public ItemRelease? ItemRelease { get; set; }
    public ItemAcquisition? ItemAcquisition { get; set; }
}
