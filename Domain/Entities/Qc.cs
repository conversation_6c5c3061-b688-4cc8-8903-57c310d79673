using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class Qc
{
    [Key] public long Id { get; set; }
    public long OrderId { get; set; }
    public long TaskId { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
    public Order? Order { get; set; }
}
