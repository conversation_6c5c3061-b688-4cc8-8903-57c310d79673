using System.ComponentModel.DataAnnotations;
using Domain.Enums;

namespace Domain.Entities;

public class Station
{
    [Key] public long Id { get; set; }
    [Required] public int Number { get; set; }
    [Required, StringLength(30)] public string Type { get; set; } = StationTypeEnum.Default;

    [Required, StringLength(30)] public string CurrentMode { get; set; } = StationModeEnum.Production;
    
    public List<StationTask>? StationTasks { get; set; }
}
