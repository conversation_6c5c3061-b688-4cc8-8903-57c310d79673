using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities;

public class QcAlter
{
    [Key] public long Id { get; set; }
    public long OrderId { get; set; }
    [Required, StringLength(255)] public string WorkerId { get; set; } = null!;
    [Required, StringLength(30)] public string Type { get; set; } = null!;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
    public Order? Order { get; set; }
    public Qc? Qc { get; set; }
}
