using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class ItemAcquisition
{
    private Order? _order;
    
    private bool _isOrderSet;

    [Key] public long Id { get; set; }
    public long OrgTransitionId { get; set; }
    public long OrderId { get; set; }
    [Required, StringLength(50)] public string SupervisorId { get; set; } = null!;
    [Required, StringLength(30)] public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    public int Amount { get; set; }
    public int BundleSize { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;

    public Order? Order
    {
        get => _order;
        set
        {
            _order = value;
            _isOrderSet = true;
        }
    }

    public bool IsOrderSet() => _isOrderSet;
    
    public List<StationTask>? StationTasks { get; set; }

    public int TotalReleasedQty { get; set; }

}
