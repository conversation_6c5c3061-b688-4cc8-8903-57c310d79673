using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class StationProductionOverflow
{
    [Key] public long Id { get; set; }
    [Required] public long StationId { get; set; }
    [Required] public long StationWorkerId { get; set; }
    [Required, StringLength(100)] public string WorkerId { get; set; } = null!;
    public int OverflowQty { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
}
