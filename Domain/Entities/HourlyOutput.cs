using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities;

public class HourlyOutput
{
    [Key] public long Id { get; set; }
    public long OrderId { get; set; }
    public int Production { get; set; }
    public int Alter { get; set; }
    public int Reject { get; set; } = 0;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
    public Order? Order { get; set; }
}
