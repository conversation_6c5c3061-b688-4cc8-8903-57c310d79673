using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities;

public class StationWorker
{
    [Key] public long Id { get; set; }
    [Required] public long StationId { get; set; }
    [Required, StringLength(255)] public string WorkerId { get; set; } = null!;
    [StringLength(255)] public string WorkerName { get; set; } = string.Empty;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime StartedAt { get; set; } = DateTime.Now;
    public DateTime? EndedAt { get; set; }
}
