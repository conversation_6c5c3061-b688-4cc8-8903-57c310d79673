using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Domain.Entities;

public class Event
{
    [Key] public Guid Id { get; set; }
    [Required, StringLength(50)] public string Type { get; set; } = null!;
    [Column(TypeName = "text"), StringLength(1000)] public string? Payload { get; set; }
    [Required] public int Floor { get; set; }
    [Required] public int Line { get; set; }
    [Required] public int Station { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
}
