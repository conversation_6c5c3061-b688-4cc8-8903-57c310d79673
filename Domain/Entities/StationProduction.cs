using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Enums;

namespace Domain.Entities;

public class StationProduction
{
    [Key] public long Id { get; set; }
    [Required] public long StationId { get; set; }
    [Required] public long TaskId { get; set; }
    [Required] public long StationWorkerId { get; set; }
    [Required, StringLength(100)] public string WorkerId { get; set; } = null!;
    [StringLength(30)] public string Type { get; set; } = ProductionTypeEnum.Production;
    [StringLength(30)] public string StationType { get; set; } = StationTypeEnum.Default;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] public DateTime CreatedAt { get; set; } = DateTime.Now;
}
