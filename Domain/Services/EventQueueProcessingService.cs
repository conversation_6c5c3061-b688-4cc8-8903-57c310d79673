using System.Threading.Channels;
using Domain.Contracts;

namespace Domain.Services;

public class UnboundedQueue<T>:IChannelQueueInterface<T>
{
    private readonly Channel<T> _channel = System.Threading.Channels.Channel.CreateUnbounded<T>();

    public bool Enqueue(T item)
    {
        return _channel.Writer.TryWrite(item);
    }

    public ValueTask<T> DequeueAsync()
    {
        return _channel.Reader.ReadAsync();
    }
    
    public int Count()
    {
        return _channel.Reader.Count;
    }

    public ValueTask<bool> WaitForNextItemAsync(CancellationToken cancellationToken = default)
    {
        return _channel.Reader.WaitToReadAsync(cancellationToken);
    }
}