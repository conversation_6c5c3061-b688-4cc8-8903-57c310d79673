using System.Security.Cryptography;
using System.Text;

namespace Domain;

public static class HmacProcessor
{
    public static string GenerateHmacSignature(string clientSecret, string method, string path, string timestamp)
    {
        string message = $"{method}:{path}:{timestamp}";
        Console.WriteLine(message);
            
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(clientSecret));

        byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
        return Convert.ToBase64String(hash);
    }
    
    public static bool ValidateHmacSignature(string clientSecret, string method, string path, string timestamp, string providedSignature)
    {
        string expectedSignature = GenerateHmacSignature(clientSecret, method, path, timestamp);
        
        return expectedSignature == providedSignature;
    }
}
