namespace Domain.Enums;

public static class ItemAcquisitionTypeEnum
{
    public const string Bundle = "bundle";
    public const string Item = "item";
    
    public static readonly string[] All = [Bundle, Item];
}

public static class LineRackExchangeStatusEnum
{
    public const string InRack = "in_rack";
    public const string InLine = "in_line";
    public const string Delivered = "delivered";
    public const string PartialDelivered = "partial_delivered";
}

public static class OrgRackExchangeStatusEnum
{
    public const string Planned = "planned";
    public const string Cutting = "cutting";
    public const string Deliverable = "deliverable";
    public const string Requested = "requested";
    public const string Approved = "approved";
    public const string Delivered = "delivered";
}


public static class RackExchangeTypeEnum
{
    public const string In = "in";
    public const string Out = "out";
}