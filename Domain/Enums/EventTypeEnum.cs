namespace Domain.Enums;

public static class EventTypeEnum
{
    public const string OnRfidSet = "on_rfid_set";
    public const string OnProductionMode = "on_pd_mode";
    public const string OnMaintenanceMode = "on_maintenance_mode";
    public const string OnAlterMode = "on_alter_mode";
    public const string OnProductionSwitch = "on_sw";
    public const string OnStatDetails = "on_stat_details";
    public const string OnRackExchange = "on_rack_exchange";
    public const string OnLog = "on_log";
    public const string OnNeedAuth = "_auth";
    public const string OnNeedStMode = "_st_mode";
    public const string OnError = "on_error";
}