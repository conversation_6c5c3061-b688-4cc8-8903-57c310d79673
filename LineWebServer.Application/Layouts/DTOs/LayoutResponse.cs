using System.Text.Json.Serialization;
using Domain.Entities;

namespace LineWebServer.Application.Layouts.DTOs;

public class LayoutItemResponse
{
    [JsonPropertyName("id")]
    public long Id { get; set; }
    
    [JsonPropertyName("layout_id")]
    public long LayoutId { get; set; }
    
    [JsonPropertyName("station_id")]
    public long StationId { get; set; }
    
    [JsonPropertyName("type")]
    public string? Type { get; set; }
    
    [JsonPropertyName("Number")]
    public int Number { get; set; }
}

public class LayoutResponse
{
    [JsonPropertyName("id")]
    public long Id { get; set; }
    
    [JsonPropertyName("label")]
    public string? Label { get; set; }
    
    [JsonPropertyName("is_active")]
    public bool IsActive { get; set; }
    
    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("layout_items")]
    public IEnumerable<LayoutItemResponse>? LayoutItems { get; set; }
}

public static class LayoutResponseBuilder
{
    private static LayoutItemResponse ToActiveLayoutItemResponse(this LayoutItem layout)
    {
        return new LayoutItemResponse
        {
            Id = layout.Id,
            LayoutId = layout.LayoutId,
            StationId = layout.StationId,
            Number = layout.Number,
            Type = layout.Type
        };
    }

    public static LayoutResponse ToActiveLayoutResponse(this Layout layout)
    {
        IList<LayoutItemResponse> layoutItems = [];
        if (layout.LayoutItems != null && layout.LayoutItems.Any())
        {
            layoutItems = layout.LayoutItems.Select(l => l.ToActiveLayoutItemResponse()).ToList();
        }
        
        return new LayoutResponse
        {
            Id = layout.Id,
            Label = layout.Label,
            IsActive = layout.IsActive,
            CreatedAt = layout.CreatedAt,
            LayoutItems = layoutItems,
        };
    }

    public static LayoutResponse ToListLayoutResponse(this Layout layout)
    {
        return new LayoutResponse
        {
            Id = layout.Id,
            Label = layout.Label,
            IsActive = layout.IsActive,
            CreatedAt = layout.CreatedAt,
        };
    }
}
