using Domain.Contracts.Services;
using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Layouts.Exceptions;

namespace LineWebServer.Application.Layouts.Services;

public class LayoutService(ILayoutRepository layoutRepository) : ILayoutService
{
    public async Task<Layout?> ActiveLayout()
    {
        return await layoutRepository.GetActiveLayout();
    }
    
    public async Task<IEnumerable<Layout>> AllLayouts()
    {
        return await layoutRepository.GetAllLayouts();
    }
    
    public async Task<Layout> ActivateLayout(long layoutId)
    {
        Layout? layout = await layoutRepository.GetLayoutById(layoutId);
        if (layout == null)
        {
            throw new LayoutNotFoundException();
        }
        return await layoutRepository.ActivateLayout(layout);
    }
    
    public async Task<Layout> LayoutDetails(long layoutId)
    {
        Layout? layout = await layoutRepository.GetLayoutById(layoutId);
     
        if (layout == null)
        {
            throw new LayoutNotFoundException();
        }
        
        return layout;
    }
}
