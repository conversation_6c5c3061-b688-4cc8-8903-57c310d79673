namespace LineWebServer.Application.Swing.Exceptions;

/// <summary>
/// Exception thrown when QC request validation fails
/// </summary>
public class QcValidationException : Exception
{
    public QcValidationException(string message) : base(message) { }
}

/// <summary>
/// Exception thrown when no active layout is found for QC operations
/// </summary>
public class ActiveLayoutNotFoundException : Exception
{
    public ActiveLayoutNotFoundException(string message) : base(message) { }
}