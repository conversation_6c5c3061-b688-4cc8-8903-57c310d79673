using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.LineMonitors.DTOs;
using LineWebServer.Application.LineMonitors.Services;

namespace LineWebServer.Application.Swing.Services;

public class SwingService(
    IChannelQueueInterface<PublishToBrokerRequest> brokerQueue,
    ISwingRepository swingRepository,
    ILayoutRepository layoutRepository, 
    ILineMonitorService lineMonitorService) : ISwingService
{
    public async Task PublishStationLoggedAuthData(int floorNo, int lineNo, int stationNo)
    {
        StationWorker? stationWorker = await swingRepository.GetLoggedStationWorkerByStationNumber(stationNo);
        if (stationWorker == null)
        {
            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNo}/auth",
                Payload = "0"
            });
            return;
        }
        
        LayoutItem? layoutItem = await layoutRepository.GetActiveLayoutLayoutItemByStationId(stationNo);
        
        if (layoutItem == null)
        {
            return;
        }
        
        CalculateStationWorkerStatResponseDto stationWorkerStat = await lineMonitorService.CalculateStationWorkerStat(stationWorker, DateTime.Today);
        brokerQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNo}/auth",
            Payload = $"1,{stationWorker.WorkerId},{layoutItem.Type},{stationWorker.WorkerName},{stationWorkerStat.Efficiency},{stationWorkerStat.Red},{stationWorkerStat.Green},{stationWorkerStat.Yellow},{stationWorkerStat.TotalPending}"
        });
    }
    
    public async Task PublishStationModeData(int floorNo, int lineNo, int stationNo)
    {
        StationMode? stationMode = await swingRepository.GetSwingStationModeByStationNumber(stationNo);

        var modeMapping = new Dictionary<string, string>
        {
            { StationModeEnum.Production, "green" },
            { StationModeEnum.Alter, "yellow" },
            { StationModeEnum.Maintenance, "red" }
        };
        
        if (stationMode == null)
        {
            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNo}/st_mode",
                Payload = modeMapping[StationModeEnum.Maintenance]
            });
            return;
        }
        
        brokerQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNo}/st_mode",
            Payload = modeMapping[stationMode.Mode]
        });
    }
}
