using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Swing.DTOs;
using LineWebServer.Application.Swing.Exceptions;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Swing.Services;

public class QcService(
    IQcRepository qcRepository,
    IStationTaskRepository stationTaskRepository,
    ILayoutRepository layoutRepository,
    ILogger<QcService> logger,
    IUnitOfWork unitOfWork) : IQcService
{
    public async Task<QcApplyResponse?> ApplyQc(ApplyQcRequest request)
    {
        var layout = await qcRepository.GetActiveLayout();
        if (layout == null)
        {
            // TODO: In qc ui show layout is not configured properly and prevent to send request
            throw new ActiveLayoutNotFoundException("No active layout found for QC operation");
        }

        LayoutItem layoutItem = await layoutRepository.GetQcLayoutItem(layout.Id);
        StationTask? stationTask = await stationTaskRepository.GetCurrentTaskOfStation(layoutItem.StationId, request.OrderId);

        if (stationTask == null)
        {
            throw new QcValidationException("No task found for QC operation");
        }

        /*
         Currently Qc model represent production for qc
         Currently QcAlters model represent alters for qc
         Currently QcReject model represent rejects for qc
         */
        
        Order? orderStat;

        await unitOfWork.BeginTransactionAsync();

        if (request.Type == QcTypeEnum.Ok)
        {
            try
            {
                int remainingQty = stationTask.ItemQty - (stationTask.TotalProduction + 1);
                await qcRepository.CreateQc(request.OrderId, stationTask.Id, remainingQty == 0);
                await unitOfWork.SaveChangesAsync();
                await unitOfWork.CommitAsync();
                logger.LogInformation("Created QC record with task ID {taskId} for order {OrderId}", stationTask.Id, request.OrderId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing QC {Type} for order {OrderId}", request.Type, request.OrderId);
                await unitOfWork.RollbackAsync();
                throw;
            }
            stationTask = await stationTaskRepository.GetCurrentTaskOfStation(layoutItem.StationId, request.OrderId);
            orderStat = await qcRepository.GetOrderStat(request.OrderId);
            return orderStat?.ToQcApplyResponse(stationTask);
        }
        

        try
        {
            foreach (var tagRequest in request.Tags ?? [])
            {
                await ProcessQcTag(request, layout, tagRequest);
            }
            
            await unitOfWork.SaveChangesAsync();
            await unitOfWork.CommitAsync();
            
            logger.LogInformation("Successfully processed tags QC {Type} for order {OrderId}", request.Type, request.OrderId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing tags for QC {Type} for order {OrderId}", request.Type, request.OrderId);
            await unitOfWork.RollbackAsync();
            throw;
        }
        
        
        stationTask = await stationTaskRepository.GetCurrentTaskOfStation(layoutItem.StationId, request.OrderId);
        orderStat = await qcRepository.GetOrderStat(request.OrderId);
        return orderStat?.ToQcApplyResponse(stationTask);
    }

    private async Task ProcessQcTag(ApplyQcRequest request, Layout layout, QcTagRequest tagRequest)
    {
        var layoutItem = await qcRepository.GetLayoutItem(layout.Id, tagRequest.StationId);
        var stationWorker = await qcRepository.GetCurrentStationWorker(tagRequest.StationId) 
                          ?? await qcRepository.GetTodayLastStationWorker(tagRequest.StationId);

        if (stationWorker == null)
        {
            logger.LogWarning("Station worker not found for station id: {StationId} at {Now}", 
                tagRequest.StationId, DateTime.Now);
        }

        // TODO: Need somehow figure out based on workerId
        string workerId = stationWorker?.WorkerId ?? $"N/A:{tagRequest.StationId}";

        if (request.Type == QcTypeEnum.Alter)
        {
            qcRepository.CreateQcAlter(new QcAlter
            {
                OrderId = request.OrderId,
                Type = layoutItem.Type,
                WorkerId = workerId
            });
        }
        else
        {
            qcRepository.CreateQcReject(new QcReject
            {
                OrderId = request.OrderId,
                Type = layoutItem.Type,
                WorkerId = workerId
            });
        }
    }
    
    public async Task<IList<Order>> GetActiveOrders()
    {
        return await qcRepository.GetActiveOrders();
    }

    public async Task<ToGetActiveOrderDetailsResource?> GetActiveOrderDetails(long orderId)
    {
        var layout = await qcRepository.GetActiveLayout();
        if (layout == null)
        {
            // TODO: In qc ui show layout is not configured properly and prevent to send request
            throw new ActiveLayoutNotFoundException("No active layout found for QC operation");
        }

        LayoutItem layoutItem = await layoutRepository.GetQcLayoutItem(layout.Id);
        StationTask? stationTask = await stationTaskRepository.GetCurrentTaskOfStation(layoutItem.StationId, orderId);

        StationTaskResponse? taskResponse = stationTask?.ToStationTaskResponse();
        Order? order = await qcRepository.GetActiveOrderDetails(orderId);
        
        return order?.ToGetActiveOrderDetailsResponse(taskResponse);
    }
}