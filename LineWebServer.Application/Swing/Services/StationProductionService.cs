using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Swing.Services;

public class StationProductionService(
    IChannelQueueInterface<PublishToBrokerRequest> brokerQueue,
    ILogger<IStationProductionService> logger,
    IStationRepository stationRepository,
    IStationModeRepository stationModeRepository,
    IStationWorkerRepository stationWorkerRepository,
    IStationProductionRepository stationProductionRepository,
    IStationTaskRepository stationTaskRepository,
    ILayoutRepository layoutRepository,
    ISupervisorOrderRepository orderRepository,
    IUnitOfWork unitOfWork
    ) : IStationProductionService
{

    public async Task HandleSwitchingFromStation(Event @event)
    {
        if (@event.Type != EventTypeEnum.OnProductionSwitch)
        {
            logger.LogError("Invalid event type: {@event.Type}", @event.Type);

            return;
        }

        logger.LogInformation("HandleOnProductionSwitchEventJob processing");

        Station? station = await stationRepository.GetStationByNumber(@event.Station);

        if (station == null)
        {
            logger.LogError("Station not found");

            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
            });

            return;
        }

        if (station.Type is StationTypeEnum.Qc or StationTypeEnum.Supervisor)
        {
            logger.LogError("QC and supervisor not allowed");

            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
            });
            
            return;
        }

        StationWorker? stationWorker = await stationWorkerRepository.GetLoggedStationWorkerById(station.Id);

        if (stationWorker == null)
        {
            logger.LogError("Station Worker not found");

            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
            });

            return;
        }
        await unitOfWork.BeginTransactionAsync();

        try
        {
            StationMode? stationMode = await stationModeRepository.GetCurrentByStation(station.Id);

            if (stationMode == null)
            {
                stationMode = new StationMode
                {
                    StationId = station.Id,
                    Mode = StationModeEnum.Production,
                    StartedAt = DateTime.Now,
                };

                await stationModeRepository.Create(stationMode);
            }

            if (stationMode.Mode == StationModeEnum.Maintenance)
            {
                brokerQueue.Enqueue(new PublishToBrokerRequest
                {
                    Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
                });

                await unitOfWork.SaveChangesAsync();
                await unitOfWork.CommitAsync();

                logger.LogInformation("Station currently maintenance mode");

                return;
            }

            string stationProductionType = stationMode.Mode switch
            {
                StationModeEnum.Alter => ProductionTypeEnum.Alter,
                StationModeEnum.Production => ProductionTypeEnum.Production,
                _ => throw new InvalidOperationException("Inappropriate station mode")
            };
            
            StationTask? stationTask = await stationTaskRepository.GetCurrentTaskOfStation(station.Id);
            // TODO: There should be station task for alters

            if (stationTask == null)
            {
                logger.LogError("Station Task should not be empty at this point.");

                brokerQueue.Enqueue(new PublishToBrokerRequest
                {
                    Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
                });

                await unitOfWork.CommitAsync();

                return;
            }

            int targetQty = stationTask.AmountType == ItemAcquisitionTypeEnum.Bundle
                ? stationTask.ItemAcquisition?.BundleSize ?? throw new InvalidOperationException("Bundle size not found")
                : stationTask.ItemQty;

            int remainingItemsToBeCompleted = targetQty - (stationTask.TotalProduction + 1);

            if (remainingItemsToBeCompleted > 0)
            {
                await stationProductionRepository.Create(new StationProduction
                {
                    StationId = station.Id,
                    TaskId = stationTask.Id,
                    CreatedAt = DateTime.Now,
                    StationWorkerId = stationWorker.Id,
                    WorkerId = stationWorker.WorkerId,
                    Type = stationProductionType,
                    StationType = station.Type
                });
                
                await unitOfWork.SaveChangesAsync();
                await unitOfWork.CommitAsync();
                return;
            }
            
            LayoutItem nextLayoutItem;

            await stationProductionRepository.Create(new StationProduction
            {
                StationId = station.Id,
                TaskId = stationTask.Id,
                CreatedAt = DateTime.Now,
                StationWorkerId = stationWorker.Id,
                WorkerId = stationWorker.WorkerId,
                Type = stationProductionType,
                StationType = station.Type
            });
                
            nextLayoutItem = await layoutRepository.GetNextLayoutItemOnMoveTaskNextStation(stationTask.StationId);

            await stationTaskRepository.MoveTaskNextStation(stationTask, nextLayoutItem);
            await unitOfWork.SaveChangesAsync();
            await unitOfWork.CommitAsync();
               
            HandleMessagingToBrokerAfterTaskProcessing(@event, nextLayoutItem);
        }
        catch (Exception)
        {
            await unitOfWork.RollbackAsync();

            throw;
        }

    }

    private void HandleMessagingToBrokerAfterTaskProcessing(Event @event, LayoutItem nextLayoutItem)
    {
        brokerQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/pts_udt",
            Payload = "1"
        });

        brokerQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{nextLayoutItem.Station?.Number ?? 0}/pts_udt",
            Payload = "1"
        });

        brokerQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/al_tbn",
            Payload = "1"
        });

    }
}
