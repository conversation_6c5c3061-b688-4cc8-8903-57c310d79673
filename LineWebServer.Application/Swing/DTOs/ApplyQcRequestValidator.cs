using Domain.Enums;
using FluentValidation;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;

namespace LineWebServer.Application.Swing.DTOs;

public class ApplyQcRequestValidator : AbstractValidator<ApplyQcRequest>
{
    public ApplyQcRequestValidator(IQcRepository qcRepository, IStationTaskRepository stationTaskRepository)
    {
        RuleFor(x => x.OrderId).Cascade(CascadeMode.Stop)
            .GreaterThan(0).WithMessage("Valid order id is required")
            .MustAsync(async (id, _) => await qcRepository.OrderExists(id)).WithMessage("Invalid order id given")
            .MustAsync(async (id, _) => await stationTaskRepository.IsExistByOrderId(id)).WithMessage("Task not exist for this order.");

        RuleFor(x => x.Type).Cascade(CascadeMode.Stop) 
            .NotEmpty().WithMessage("Type is required.")
            .Must(t => QcTypeEnum.All.Contains(t)).WithMessage("Invalid type given.");

        RuleFor(x => x.Tags).Cascade(CascadeMode.Stop)
            .NotNull().WithMessage("Tags are required.")
            .Must(tags => tags?.Any() ?? false).WithMessage("At least one tag is required.")
            .When(x => x.Type is QcTypeEnum.Alter or QcTypeEnum.Reject);

        RuleForEach(x => x.Tags)
            .ChildRules(tag =>
            {
                tag.RuleFor(t => t.StationId)
                    .Cascade(CascadeMode.Stop) 
                    .NotEmpty().WithMessage("StationId is required.")
                    .GreaterThan(0).WithMessage("StationId must be greater than 0.")
                    .MustAsync(async (id, _) => await qcRepository.StationExists(id))
                    .WithMessage("StationId does not exist.");
            });
    }
}
