using System.Text.Json.Serialization;
using Domain.Entities;
using Domain.Enums;

namespace LineWebServer.Application.Swing.DTOs;

public class StationTaskResponse
{
    public long Id { get; set; }
    public long StationId { get; set; }
    public long ItemAcquisitionId { get; set; }
    public long ItemReleaseId { get; set; }
    public long OrderId { get; set; }
    public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    public int ItemQty { get; set; }
    public string Status { get; set; } = StationTaskStatusEnum.Processing;
    public int TotalProduction { get; set; }
    public DateTime? CreatedAt { get; set; } = DateTime.Now;
}

public static class StationTaskResponseBuilder
{
    public static StationTaskResponse? ToStationTaskResponse(this StationTask stationTask)
    {
        return new StationTaskResponse
        {
            Id = stationTask.Id,
            OrderId = stationTask.OrderId,
            StationId = stationTask.StationId,
            ItemAcquisitionId = stationTask.ItemAcquisitionId,
            ItemReleaseId = stationTask.ItemReleaseId,
            AmountType = stationTask.AmountType,
            ItemQty = stationTask.ItemQty,
            Status = stationTask.Status,
            TotalProduction = stationTask.TotalProduction,
        };
    }
}