using System.ComponentModel.DataAnnotations;

namespace LineWebServer.Application.Swing.DTOs;

public class QcTagRequest
{
    [Required]
    public long StationId { get; set; }
}

public class ApplyQcRequest
{
    [Required]
    public long OrderId { get; set; }
    
    [Required]
    [StringLength(30)]
    public string Type { get; set; } = string.Empty;
    
    public IEnumerable<QcTagRequest>? Tags { get; set; }
}