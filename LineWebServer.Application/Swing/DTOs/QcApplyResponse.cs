using System.Text.Json.Serialization;
using Domain.Entities;

namespace LineWebServer.Application.Swing.DTOs;

public class OrderStatResponse
{
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}

public class QcApplyResponse
{
    public StationTaskResponse? Task { get; set; }
    public OrderStatResponse? OrderStat { get; set; }
}

public class ToGetActiveOrderDetailsResource
{
    public long Id { get; set; }
    public string? Label { get; set; }
    public string BuyerName { get; set; } = null!;
    public string OrderId { get; set; } = null!;
    public string? Country { get; set; }
    public int OrderQuantity { get; set; }
    public int HourlyTarget { get; set; }
    public DateTime AssignedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }

    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
    
    public StationTaskResponse? Task { get; set; }
}

public static class QcOrderResponseBuilder
{
    public static QcApplyResponse ToQcApplyResponse(this Order order, StationTask? stationTask)
    {
        return new QcApplyResponse
        {
            OrderStat = new OrderStatResponse
            {
                TotalProductions = order.TotalProductions,
                TotalAlters = order.TotalAlters,
                TotalRejects = order.TotalRejects
            },
            Task = stationTask?.ToStationTaskResponse(),
        };
    }
    
    public static ToGetActiveOrderDetailsResource ToGetActiveOrderDetailsResponse(this Order order, StationTaskResponse? taskResponse)
    {
        return new ToGetActiveOrderDetailsResource
        {
            Id = order.Id,
            Label = order.Label,
            BuyerName = order.BuyerName,
            OrderId = order.OrderId,
            Country = order.Country,
            OrderQuantity = order.OrderQuantity,
            HourlyTarget = order.HourlyTarget,
            AssignedAt = order.AssignedAt,
            StartedAt = order.StartedAt,
            EndedAt = order.EndedAt,
            TotalProductions = order.TotalProductions,
            TotalAlters = order.TotalAlters,
            TotalRejects = order.TotalRejects,
            Task = taskResponse,
        };
    }
}

