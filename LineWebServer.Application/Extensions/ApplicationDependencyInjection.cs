using System.Transactions;
using Domain.Contracts.Services;
using Domain.Enums;
using FluentValidation;
using Hangfire;
using Hangfire.MySql;
using LineWebServer.Application.Layouts.Services;
using LineWebServer.Application.LineMonitors.Services;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Settings.Services;
using LineWebServer.Application.Supervisors.Services;
using LineWebServer.Application.Swing.DTOs;
using LineWebServer.Application.Swing.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Net.Http.Headers;

namespace LineWebServer.Application.Extensions;

public static class ApplicationDependencyInjection
{
    public static IServiceCollection AddHangfireServiceFromApplication(this IServiceCollection services, IConfiguration config, bool isCliApp)
    {
        if(isCliApp)
        {
            return services;
        }

        services.AddHangfire(configuration => configuration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseStorage(
                new MySqlStorage(
                    config.GetSection("HangfireConnection").Value,
                    new MySqlStorageOptions
                    {
                        TransactionIsolationLevel = (IsolationLevel?)System.Data.IsolationLevel.ReadCommitted,
                        QueuePollInterval = TimeSpan.FromSeconds(1),
                        JobExpirationCheckInterval = TimeSpan.FromHours(1),
                        CountersAggregateInterval = TimeSpan.FromMinutes(5),
                        PrepareSchemaIfNecessary = true,
                        DashboardJobListLimit = 10000,
                        TransactionTimeout = TimeSpan.FromMinutes(1),
                        TablesPrefix = "Hangfire"
                    }
                )
            )
        );

        services.AddHangfireServer();
        
        return services;
    }

    public static IServiceCollection AddValidatorsFromApplication(this IServiceCollection services)
    {
        services.AddValidatorsFromAssemblyContaining<ApplyQcRequestValidator>();
        services.AddValidatorsFromAssemblyContaining<UpdateOrderStatusRequestValidator>();
        services.AddValidatorsFromAssemblyContaining<CreateOrderRequestValidator>();
        services.AddValidatorsFromAssemblyContaining<AcceptBundleFromCuttingRequestValidator>();
        services.AddValidatorsFromAssemblyContaining<DeliverBundleFromRackRequestValidator>();
        return services;
    }

    public static IServiceCollection AddHttpClientsFromApplication(this IServiceCollection services, IConfiguration configuration)
    {
        string orgServerBaseUrl = configuration["OrgServer:BaseUrl"] ?? throw new InvalidOperationException("OrgServer:BaseUrl is missing from configuration");
        string orgServerClientId = configuration["OrgServer:ClientId"] ?? throw new InvalidOperationException("OrgServer:ClientId is missing from configuration");

        services.AddHttpClient(HttpClientTypeEnum.OrgServer, httpClient =>
        {
            httpClient.BaseAddress = new Uri(orgServerBaseUrl);

            httpClient.DefaultRequestHeaders.Add(
                HeaderNames.Accept, "application/json");

            httpClient.DefaultRequestHeaders.Add(
                "ClientId", orgServerClientId);
            
            httpClient.Timeout = TimeSpan.FromSeconds(5);
        });
        
        return services;
    }
    
    public static IServiceCollection RegisterServices(this IServiceCollection services)
    {
        services.AddTransient<ILayoutService, LayoutService>();
        services.AddTransient<ISupervisorOrderService, SupervisorOrderService>();
        services.AddTransient<ILineMonitorService, LineMonitorService>();
        services.AddTransient<IQcService, QcService>();
        services.AddTransient<ISettingService, SettingService>();
        services.AddTransient<IItemAcquisitionService, ItemAcquisitionService>();
        services.AddTransient<ISupervisorService, SupervisorService>();
        services.AddTransient<IStationProductionService, StationProductionService>();
        services.AddTransient<ISwingService, SwingService>();

        return services;
    }
}
