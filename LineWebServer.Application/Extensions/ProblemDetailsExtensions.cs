using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace LineWebServer.Application.Extensions;

public static class ProblemDetailsExtensions
{
    public static ProblemDetails RespondWithValidationErrors(this ProblemDetails problemDetails)
    {
        problemDetails.Title ??= "Bad Request";
        problemDetails.Detail ??= "Validation failed";
        problemDetails.Status ??= 400;
        problemDetails.Extensions["errors"] ??= null;
        return problemDetails;

    }
    
    public static ProblemDetails RespondWithValidationErrors(this ProblemDetails problemDetails, IList<ValidationFailure> errors)
    {
        var errorDictionary = errors
            .GroupBy(e => e.PropertyName)
            .ToDictionary(
                g => g.Key,
                g => g.Select(e => e.ErrorMessage).ToArray()
            );

        problemDetails.Extensions["errors"] = errorDictionary;

        return problemDetails;
    }
    
    public static ModelStateDictionary RespondForValidationProblem(this ModelStateDictionary modelState, IList<ValidationFailure> errors)
    {
        foreach (var error in errors)
        {
            modelState.AddModelError(error.PropertyName, error.ErrorMessage);
        }
        return modelState;
    }
}
