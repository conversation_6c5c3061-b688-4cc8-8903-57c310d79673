<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Domain\Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Empty\DTOs\"/>
        <Folder Include="Empty\Exceptions\"/>
        <Folder Include="Empty\Services\"/>
        <Folder Include="LineMonitors\Exceptions\"/>
        <Folder Include="LineMonitors\Services\"/>
        <Folder Include="Settings\DTOs\" />
        <Folder Include="Settings\Exceptions\" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.SmartEnum" Version="8.2.0"/>
        <PackageReference Include="FluentValidation" Version="11.11.0"/>
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2"/>

        <PackageReference Include="Hangfire.Core" Version="1.8.18"/>
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.18"/>
        <PackageReference Include="Hangfire.MySqlStorage" Version="2.0.3"/>
    </ItemGroup>

</Project>
