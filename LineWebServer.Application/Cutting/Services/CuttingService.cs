using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Cutting.DTOs;
using LineWebServer.Application.Cutting.DTOs.Repositories;
using LineWebServer.Application.Cutting.Exceptions;
using LineWebServer.Application.Supervisors.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Cutting.Services;

public sealed class CuttingService(IRackExchangeRepository rackExchangeRepository, ISupervisorService supervisorService, IUnitOfWork unitOfWork, IChannelQueueInterface<PublishToBrokerRequest> brokerQueue, IConfiguration configuration, ILogger<CuttingService> logger) : ICuttingService
{
    public async Task<IEnumerable<RackExchange>> Get(GetRackExchangesRequest dto)
    {
        var response = await rackExchangeRepository.Get(dto);

        return response;
    }
    
    public async Task AcceptApprovedBundleFromCutting(AcceptBundleFromCuttingRequest request)
    {
        Order? order = await rackExchangeRepository.GetOrderByOrderId(request.OrderId, request.AssignedDate);
        if (order == null)
        {
            throw new OrderNotFoundForRackExchangeException("Order does not exist");
        }

        string? loggedInSupervisorId = await supervisorService.GetLoggedInSupervisorId();
        
        if (loggedInSupervisorId == null)
        {
            throw new LoggedInSupervisorNotFoundForRackExchangeException("Logged in supervisor not found");
        }

        RackExchange rackExchange = new RackExchange()
        {
            OrderId = order.Id,
            SupervisorId = loggedInSupervisorId,
            ExchangeType = RackExchangeTypeEnum.In,
            AmountType = request.AmountType,
            Status = LineRackExchangeStatusEnum.InRack,
            Amount = request.Amount,
            OrgRackExchangeId = request.Id
        };
        await rackExchangeRepository.CreateAsync(rackExchange);
        
        await unitOfWork.SaveChangesAsync();        
    }

    public async Task DeliverBundleFromRack(DeliverBundleFromRackRequest request)
    {
        int stationNumber = await rackExchangeRepository.DeliveryFromRack(new DeliverBundleFromRackRequest
        {
            RackExchangeId = request.RackExchangeId,
            Amount = request.Amount,
        });
        await unitOfWork.SaveChangesAsync();
        
        int floorNo = configuration.GetValue<int?>("LayoutPosition:FloorNo")
                      ?? throw new InvalidOperationException("Floor number not found in configuration.");
        int lineNo = configuration.GetValue<int?>("LayoutPosition:LineNo")
                     ?? throw new InvalidOperationException("Line number not found in configuration.");

        try
        {
            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNumber}/pts_udt"
            });
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error publishing to broker");
        }
    }
}
