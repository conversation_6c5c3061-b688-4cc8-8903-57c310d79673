using Domain.Entities;
using LineWebServer.Application.Cutting.DTOs;
using LineWebServer.Application.Cutting.DTOs.Repositories;

namespace LineWebServer.Application.Cutting.Services;

public interface ICuttingService
{
    Task<IEnumerable<RackExchange>> Get(GetRackExchangesRequest dto);
    Task AcceptApprovedBundleFromCutting(AcceptBundleFromCuttingRequest request);
    Task DeliverBundleFromRack(DeliverBundleFromRackRequest request);
}
