using FluentValidation;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Cutting.DTOs.Repositories;

namespace LineWebServer.Application.Cutting.DTOs;

public class DeliverBundleFromRackRequestValidator: AbstractValidator<DeliverBundleFromRackRequest>
{
    public DeliverBundleFromRackRequestValidator(IRackExchangeRepository repository)
    {
        RuleFor(x => x.RackExchangeId).Cascade(CascadeMode.Stop)
            .GreaterThan(0).WithMessage("Exchange id must greater than 0")
            .MustAsync(async (id, _) => await repository.IsExist(new RackExchangeRepositoryIsExistDto { Id = id }))
            .WithMessage("Invalid exchange id given.");
        
        RuleFor(x => x.Amount).Cascade(CascadeMode.Stop)
            .MustAsync(async (model, x, _) => 
                x <= await repository.GetAmountDeliverableToLine(model.RackExchangeId)).WithMessage("Amount must not greater than deliverable amount.");
    }    
}
