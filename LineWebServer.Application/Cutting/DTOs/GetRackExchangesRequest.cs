namespace LineWebServer.Application.Cutting.DTOs;

public sealed class GetRackExchangesRequest
{
    private long? _id;
    private long? _orderId;
    private string[] _status;
    private long? _parentId;

    private bool _isIdSet;
    private bool _isOrderIdSet;
    private bool _isStatusSet;
    private bool _isParentIdSet;

    public long? Id
    {
        get => _id;
        set
        {
            _id = value;
            _isIdSet = true;
        }
    }

    public long? OrderId
    {
        get => _orderId;
        set
        {
            _orderId = value;
            _isOrderIdSet = true;
        }
    }
    
    public string[] Status
    {
        get => _status;
        set
        {
            _status = value;
            _isStatusSet = true;
        }
    }

    public long? ParentId
    {
        get => _parentId;
        set
        {
            _parentId = value;
            _isParentIdSet = true;
        }
    }

    public bool IsIdSet() => _isIdSet;
    public bool IsOrderIdSet() => _isOrderIdSet;
    public bool IsStatusSet() => _isStatusSet;
    public bool IsParentIdSet() => _isParentIdSet;
}
