namespace LineWebServer.Application.Supervisors.DTOs.Repository;

public class IsOrderExistDto
{
    private long _id;
    private bool _orderStarted;
    private bool _orderEnded;
    private string _orderId = null!;

    private bool _isIdSet;
    private bool _isOrderStartedSet;
    private bool _isOrderEndedSet;
    private bool _isOrderIdSet;
    
    public long Id
    {
        get => _id;
        set
        {
            _id = value;
            _isIdSet = true;
        }
    }

    public bool OrderStarted
    {
        get => _orderStarted;
        set
        {
            _orderStarted = value;
            _isOrderStartedSet = true;
        }
    }

    public bool OrderEnded
    {
        get => _orderEnded;
        set
        {
            _orderEnded = value;
            _isOrderEndedSet = true;
        }
    }

    public string OrderId
    {
        get => _orderId;
        set
        {
            _orderId = value;
            _isOrderIdSet = true;
        }
    }
    public bool IsIdSet() => _isIdSet;
    public bool IsOrderStarted() => _isOrderStartedSet;
    public bool IsOrderEndedSet() => _isOrderEndedSet;
    public bool IsOrderIdSet() => _isOrderIdSet;
    
}
