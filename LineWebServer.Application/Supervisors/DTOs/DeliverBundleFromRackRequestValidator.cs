using FluentValidation;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs.Repository;

namespace LineWebServer.Application.Supervisors.DTOs;

public class DeliverBundleFromRackRequestValidator: AbstractValidator<ReleaseItemAcquisitionRequest>
{
    public DeliverBundleFromRackRequestValidator(IItemAcquisitionRepository repository)
    {
        RuleFor(x => x.ItemAcquisitionId).Cascade(CascadeMode.Stop)
            .GreaterThan(0).WithMessage("Exchange id must greater than 0")
            .MustAsync(async (_, id, context, _) =>
            {
                bool isExist = await repository.IsExist(new RackExchangeRepositoryIsExistDto { Id = id });
                context.RootContextData["IsAcquisitionExist"] = isExist;
                return isExist;
            }).WithMessage("Invalid exchange id given.");
        
        RuleFor(x => x.AmountToBeReleased)
            .MustAsync(async (model, x, _) =>
            {
                int releasableQty = await repository.GetReleasableQtyFromAcquisition(model.ItemAcquisitionId);
                return x <= releasableQty;
            }).WithMessage("Amount must not greater than deliverable amount.")
            .When((_, context) =>
            {
                if (!context.RootContextData.TryGetValue("IsAcquisitionExist", out object? isExist))
                {
                    return false;
                }

                return (bool)isExist;
            });
    }    
}
