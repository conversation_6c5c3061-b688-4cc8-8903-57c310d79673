namespace LineWebServer.Application.Supervisors.DTOs;

public sealed class GetItemAcquisitionRequest
{
    private long? _id;
    private long? _orderId;

    private bool _isIdSet;
    private bool _isOrderIdSet;

    public long? Id
    {
        get => _id;
        set
        {
            _id = value;
            _isIdSet = true;
        }
    }

    public long? OrderId
    {
        get => _orderId;
        set
        {
            _orderId = value;
            _isOrderIdSet = true;
        }
    }
    
    public bool IsIdSet() => _isIdSet;
    public bool IsOrderIdSet() => _isOrderIdSet;
}
