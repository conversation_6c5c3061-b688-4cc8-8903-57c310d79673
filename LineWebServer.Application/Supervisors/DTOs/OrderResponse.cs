using Domain.Entities;

namespace LineWebServer.Application.Supervisors.DTOs;

public class OrderResponse
{
    public long Id { get; set; }
    public string? Label { get; set; }
    public required string BuyerName { get; set; }
    public string? Country { get; set; }
    public required int OrderQuantity { get; set; }
    public required int HourlyTarget { get; set; }
    public required DateTime AssignedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
}

public class OrderResponseWithStat:OrderResponse
{
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}

public static class OrderResponseBuilder
{
    public static OrderResponse ToOrderResponse(this Order order)
    {
        return new OrderResponse
        {
            Id = order.Id,
            Label = order.Label,
            BuyerName = order.BuyerName,
            Country = order.Country,
            OrderQuantity = order.OrderQuantity,
            HourlyTarget = order.HourlyTarget,
            AssignedAt = order.AssignedAt,
            StartedAt = order.StartedAt,
            EndedAt = order.EndedAt
        };
    }
    public static OrderResponseWithStat ToOrderResponseWithStat(this Order order)
    {
        return new OrderResponseWithStat
        {
            Id = order.Id,
            Label = order.Label,
            BuyerName = order.BuyerName,
            Country = order.Country,
            OrderQuantity = order.OrderQuantity,
            HourlyTarget = order.HourlyTarget,
            AssignedAt = order.AssignedAt,
            StartedAt = order.StartedAt,
            EndedAt = order.EndedAt,
            TotalProductions = order.TotalProductions,
            TotalAlters = order.TotalAlters,
            TotalRejects = order.TotalRejects
        };
    }
}
