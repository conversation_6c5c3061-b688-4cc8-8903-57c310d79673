using System.ComponentModel.DataAnnotations;
using FluentValidation;

namespace LineWebServer.Application.Supervisors.DTOs;

public class SupervisorCreateOrderRequest
{
    [Required]
    public string OrderId { get; set; } = null!;
    public string Label { get; set; } = null!;
    public string? BuyerName { get; set; } = null!;
    public string? Country { get; set; } = null!;
    
    [Required]
    public int OrderQuantity { get; set; } = 0;

    [Required]
    public int HourlyTarget { get; set; }
}

public class CreateOrderRequestValidator : AbstractValidator<SupervisorCreateOrderRequest>
{
    public CreateOrderRequestValidator()
    {
        RuleFor(x => x.Label).Cascade(CascadeMode.Stop)
            .NotNull()
            .NotEmpty().WithMessage("Label is required.")
            .MaximumLength(50).WithMessage("Label must be less than 50 characters.");
        
        RuleFor(x => x.OrderId).Cascade(CascadeMode.Stop)
            .NotNull()
            .NotEmpty().WithMessage("Order Id is required.")
            .MaximumLength(50).WithMessage("Label must be less than 50 characters.");
        
        RuleFor(x => x.BuyerName).Cascade(CascadeMode.Stop)
            .MaximumLength(50).WithMessage("Label must be less than 50 characters.");
        
        RuleFor(x => x.Country).Cascade(CascadeMode.Stop)
            .MaximumLength(50).WithMessage("Label must be less than 50 characters.");

        RuleFor(x => x.OrderQuantity)
            .GreaterThan(0);

        RuleFor(x => x.HourlyTarget)
            .GreaterThan(0);

    }
}
