using System.Text.Json.Serialization;
using FluentValidation;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs.Repository;

namespace LineWebServer.Application.Supervisors.DTOs;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum OrderStatusTypeEnum
{
    Start = 1,
    Finish = 2,
}

public class SupervisorUpdateOrderStatusRequest
{
    public long OrderId { get; set; }
    public OrderStatusTypeEnum Type { get; set; }
}

public class UpdateOrderStatusRequestValidator : AbstractValidator<SupervisorUpdateOrderStatusRequest>
{
    public UpdateOrderStatusRequestValidator(ISupervisorOrderRepository repository)
    {
        RuleFor(x => x)
            .Custom((model, context) =>
            {
                if (!Enum.IsDefined(typeof(OrderStatusTypeEnum), model.Type))
                {
                    context.AddFailure("Type", "Invalid type given.");
                    context.RootContextData["IsValidType"] = false;
                }

                context.RootContextData["IsValidType"] = true;
            });


        RuleFor(x => x.OrderId).Cascade(CascadeMode.Stop)
            .NotEmpty().WithMessage("Order Id is required.")
            .GreaterThan(0).WithMessage("Invalid order id given")
            .MustAsync(async (_, orderId, context, _) =>
            {
                bool isOrderExist = await repository.IsExist(new IsOrderExistDto { Id = orderId });
                context.RootContextData["OrderExist"] = isOrderExist;

                return isOrderExist;
            })
            .WithMessage("Order does not exist.")
            ;


        When((model, context) =>
        {
            if (!IsValidatable(context, model))
            {
                return false;
            }

            return model.Type == OrderStatusTypeEnum.Start;

        }, () =>
        {
            RuleFor(x => x.OrderId).Cascade(CascadeMode.Stop)
                .MustAsync(async (orderId, _) =>
                {
                    bool isOrderExist = await repository.IsExist(new IsOrderExistDto { Id = orderId, OrderStarted = false });

                    return isOrderExist;
                }).WithMessage("Order is already started or ended")
                .MustAsync(async (_, _) => !await repository.IsExist(new IsOrderExistDto { OrderStarted = true }))
                .WithMessage("Another order is already is in progress");
        });

        When((model, context) =>
        {
            if (!IsValidatable(context, model))
            {
                return false;
            }

            return model.Type == OrderStatusTypeEnum.Finish;

        }, () =>
        {
            RuleFor(x => x.OrderId).MustAsync(async (orderId, _) =>
            {
                bool isOrderExist = await repository.IsExist(new IsOrderExistDto { Id = orderId, OrderStarted = true });

                return isOrderExist;
            }).WithMessage("Order must be started and not ended.");
        });


    }

    private static bool IsValidatable(ValidationContext<SupervisorUpdateOrderStatusRequest> context, SupervisorUpdateOrderStatusRequest model)
    {
        if (!context.RootContextData.TryGetValue("IsValidType", out _))
        {
            return false;
        }

        if (!context.RootContextData.TryGetValue("OrderExist", out object? isOrderExist))
        {
            return false;
        }

        if (!(bool)isOrderExist)
        {
            return false;
        }

        if (!Enum.TryParse<OrderStatusTypeEnum>(model.Type.ToString(), out _))
            return false;

        return true;
    }
}
