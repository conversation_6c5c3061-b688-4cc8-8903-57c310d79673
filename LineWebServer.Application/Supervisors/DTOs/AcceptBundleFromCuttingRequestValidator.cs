using Domain.Enums;
using FluentValidation;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs.Repository;

namespace LineWebServer.Application.Supervisors.DTOs;
public sealed class AcceptBundleFromCuttingRequestValidator: AbstractValidator<AcceptBundleFromCuttingRequest>
{
    public AcceptBundleFromCuttingRequestValidator(ISupervisorOrderRepository repository)
    {
        RuleFor(x => x.OrderId).Cascade(CascadeMode.Stop)
            .NotEmpty().WithMessage("Order id is required.")
            .MustAsync(async (orderId, _) => await repository.IsExist(new IsOrderExistDto { OrderId = orderId })).WithMessage("Invalid order id given.");
        
        RuleFor(x => x.AmountType)
            .NotNull().WithMessage("Amount Type is required")
            .NotEmpty().WithMessage("Amount Type is required")
            .Must(amountType => ItemAcquisitionTypeEnum.All.Contains(amountType)).WithMessage($"Allowed amount types are: {string.Join(",", ItemAcquisitionTypeEnum.All)}");

        RuleFor(x => x.Amount).Cascade(CascadeMode.Stop)
            .Must(x => x > 0).WithMessage("Amount must be greater than 0.");
    }
}
