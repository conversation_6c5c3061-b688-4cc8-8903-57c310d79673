using Domain.Entities;

namespace LineWebServer.Application.Supervisors.DTOs.Response;

public class OrderStatResource
{
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}
public class ActivityResource
{
    public long LayoutId { get; set; }
    public int StationNo { get; set; }
    public string WorkerName { get; set; } = null!;
    public decimal Efficiency { get; set; }
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}

public class SupervisorOrderActivityResource
{
    public OrderStatResource Stat { get; set; } = null!;
    public IEnumerable<ActivityResource> Activities { get; set; } = [];
    
}

public static class OrderResponseBuilder
{
    public static SupervisorOrderActivityResource ToSupervisorOrderActivityResource(this Order order, IEnumerable<ActivityResource> activities)
    {
        return new SupervisorOrderActivityResource
        {
            Stat = new OrderStatResource
            {
                TotalProductions = order.TotalProductions,
                TotalAlters = order.TotalAlters,
                TotalRejects = order.TotalRejects
            },
            Activities = activities
        };
    }
}