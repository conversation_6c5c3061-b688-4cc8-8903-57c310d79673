using Domain.CommonResource;
using Domain.Entities;
using Domain.Enums;

namespace LineWebServer.Application.Supervisors.DTOs.Response;

public class ItemAcquisitionResource
{
    public long Id { get; set; }
    public long OrgTransitionId { get; set; }
    public long OrderId { get; set; }
    public string SupervisorId { get; set; } = null!;
    public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    public int Amount { get; set; }
    public int BundleSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public int TotalReleasedQty { get; set; }
}

public static class ItemAcquisitionResponseBuilder
{
    public static ItemAcquisitionResource ToItemAcquisitionResource(this ItemAcquisition itemAcquisition)
    {
        return new ItemAcquisitionResource
        {
            Id = itemAcquisition.Id,
            OrgTransitionId = itemAcquisition.OrgTransitionId,
            OrderId = itemAcquisition.OrderId,
            SupervisorId = itemAcquisition.SupervisorId,
            AmountType = itemAcquisition.AmountType,
            Amount = itemAcquisition.Amount,
            BundleSize = itemAcquisition.BundleSize,
            CreatedAt = itemAcquisition.CreatedAt,
            TotalReleasedQty = itemAcquisition.TotalReleasedQty,
        };
    }

    public static CommonPaginationResource<ItemAcquisitionResource> ToItemAcquisitionResourcePagination(
        this CommonPaginationResource<ItemAcquisition> paginationResource)
    {
        return new CommonPaginationResource<ItemAcquisitionResource>
        {
            Items = paginationResource.Items.Select(item => item.ToItemAcquisitionResource()),
            TotalCount = paginationResource.TotalCount,
            TotalPages = paginationResource.TotalPages,
            Page = paginationResource.Page,
            PerPage = paginationResource.PerPage
        };
    }
}

