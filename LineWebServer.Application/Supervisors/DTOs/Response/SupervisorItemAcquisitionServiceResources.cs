using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Domain.Entities;
using Domain.Enums;

namespace LineWebServer.Application.Supervisors.DTOs.Response;

public class ItemAcquisitionResource
{
    public long Id { get; set; }
    public long OrgTransitionId { get; set; }
    public long OrderId { get; set; }
    public string SupervisorId { get; set; } = null!;
    public string AmountType { get; set; } = ItemAcquisitionTypeEnum.Bundle;
    public int Amount { get; set; }
    public int BundleSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public int TotalReleasedQty { get; set; }

    // Navigation properties (optional for resource)
    public Order? Order { get; set; }
    public List<StationTask>? StationTasks { get; set; }
}

public class OrderStatResource
{
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}

public class ActivityResource
{
    public long LayoutId { get; set; }
    public int StationNo { get; set; }
    public string WorkerName { get; set; } = null!;
    public decimal Efficiency { get; set; }
    public int TotalProductions { get; set; }
    public int TotalAlters { get; set; }
    public int TotalRejects { get; set; }
}

public class SupervisorOrderActivityResource
{
    public OrderStatResource Stat { get; set; } = null!;
    public IEnumerable<ActivityResource> Activities { get; set; } = [];
}

public static class ItemAcquisitionResponseBuilder
{
    public static ItemAcquisitionResource ToItemAcquisitionResource(this ItemAcquisition itemAcquisition)
    {
        return new ItemAcquisitionResource
        {
            Id = itemAcquisition.Id,
            OrgTransitionId = itemAcquisition.OrgTransitionId,
            OrderId = itemAcquisition.OrderId,
            SupervisorId = itemAcquisition.SupervisorId,
            AmountType = itemAcquisition.AmountType,
            Amount = itemAcquisition.Amount,
            BundleSize = itemAcquisition.BundleSize,
            CreatedAt = itemAcquisition.CreatedAt,
            TotalReleasedQty = itemAcquisition.TotalReleasedQty,
            Order = itemAcquisition.Order,
            StationTasks = itemAcquisition.StationTasks
        };
    }
}

public static class OrderResponseBuilder
{
    public static SupervisorOrderActivityResource ToSupervisorOrderActivityResource(this Order order, IEnumerable<ActivityResource> activities)
    {
        return new SupervisorOrderActivityResource
        {
            Stat = new OrderStatResource
            {
                TotalProductions = order.TotalProductions,
                TotalAlters = order.TotalAlters,
                TotalRejects = order.TotalRejects
            },
            Activities = activities
        };
    }
}