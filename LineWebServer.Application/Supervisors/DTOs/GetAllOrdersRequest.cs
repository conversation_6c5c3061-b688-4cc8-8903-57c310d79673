namespace LineWebServer.Application.Supervisors.DTOs;

public class GetAllOrdersRequest
{
    private long? _id;
    private string? _label;
    private string? _buyerName;
    private string? _country;
    private int? _orderQuantity;
    private int? _hourlyTarget;
    private DateTime? _assignedAt;
    private DateTime? _startedAt;
    private DateTime? _endedAt;
    private IList<string>? _expands;

    private bool _isIdSet;
    private bool _isLabelSet;
    private bool _isBuyerNameSet;
    private bool _isCountrySet;
    private bool _isOrderQuantitySet;
    private bool _isHourlyTargetSet;
    private bool _isAssignedAtSet;
    private bool _isStartedAtSet;
    private bool _isEndedAtSet;
    private bool _isExpandsSet;

    public long? Id
    {
        get => _id;
        set
        {
            _id = value;
            _isIdSet = true;
        }
    }

    public string? Label
    {
        get => _label;
        set
        {
            _label = value;
            _isLabelSet = true;
        }
    }

    public string? BuyerName
    {
        get => _buyerName;
        set
        {
            _buyerName = value;
            _isBuyerNameSet = true;
        }
    }

    public string? Country
    {
        get => _country;
        set
        {
            _country = value;
            _isCountrySet = true;
        }
    }

    public int? OrderQuantity
    {
        get => _orderQuantity;
        set
        {
            _orderQuantity = value;
            _isOrderQuantitySet = true;
        }
    }

    public int? HourlyTarget
    {
        get => _hourlyTarget;
        set
        {
            _hourlyTarget = value;
            _isHourlyTargetSet = true;
        }
    }

    public DateTime? AssignedAt
    {
        get => _assignedAt;
        set
        {
            _assignedAt = value;
            _isAssignedAtSet = true;
        }
    }

    public DateTime? StartedAt
    {
        get => _startedAt;
        set
        {
            _startedAt = value;
            _isStartedAtSet = true;
        }
    }

    public DateTime? EndedAt
    {
        get => _endedAt;
        set
        {
            _endedAt = value;
            _isEndedAtSet = true;
        }
    }
    public IList<string>? Expands
    {
        get => _expands;
        set
        {
            _expands = value;
            _isExpandsSet = true;
        }
    }

    public bool IsIdSet() => _isIdSet;
    public bool IsLabelSet() => _isLabelSet;
    public bool IsBuyerNameSet() => _isBuyerNameSet;
    public bool IsCountrySet() => _isCountrySet;
    public bool IsOrderQuantitySet() => _isOrderQuantitySet;
    public bool IsHourlyTargetSet() => _isHourlyTargetSet;
    public bool IsAssignedAtSet() => _isAssignedAtSet;
    public bool IsStartedAtSet() => _isStartedAtSet;
    public bool IsEndedAtSet() => _isEndedAtSet;
    public bool IsExpandsSet() => _isExpandsSet;
}
