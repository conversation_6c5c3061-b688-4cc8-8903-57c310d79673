using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Settings.Services;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Response;
using LineWebServer.Application.Supervisors.Exceptions;

namespace LineWebServer.Application.Supervisors.Services;

public class SupervisorOrderService(ISupervisorOrderRepository supervisorOrderRepository, ISettingService settingService, IUnitOfWork unitOfWork) : ISupervisorOrderService
{
    public async Task<IEnumerable<Order>> GetSupervisorOrders(GetAllOrdersRequest request)
    {
        return await supervisorOrderRepository.GetAllAsync(request);
    }

    public async Task<SupervisorOrderActivityResource> GetSupervisorOrderActivity(long orderId)
    {
        await Task.Delay(100);
        
        Order order = new Order
        {
            Id = orderId,
            TotalProductions = 1250,
            TotalAlters = 45,
            TotalRejects = 23
        };

        var mockActivities = new List<ActivityResource>
        {
            new ActivityResource
            {
                LayoutId = 1,
                StationNo = 101,
                WorkerName = 1001, // Assuming this is worker ID
                Efficiency = 85,
                TotalProductions = 320,
                TotalAlters = 12,
                TotalRejects = 5
            },
            new ActivityResource
            {
                LayoutId = 1,
                StationNo = 102,
                WorkerName = 1002,
                Efficiency = 92,
                TotalProductions = 298,
                TotalAlters = 8,
                TotalRejects = 3
            },
            new ActivityResource
            {
                LayoutId = 1,
                StationNo = 103,
                WorkerName = 1003,
                Efficiency = 78,
                TotalProductions = 275,
                TotalAlters = 15,
                TotalRejects = 8
            },
            new ActivityResource
            {
                LayoutId = 1,
                StationNo = 104,
                WorkerName = 1004,
                Efficiency = 88,
                TotalProductions = 357,
                TotalAlters = 10,
                TotalRejects = 7
            }
        };

        return order.ToSupervisorOrderActivityResource(mockActivities);
    }

    public async Task<Order> GetActiveOrder()
    {
        var order = await supervisorOrderRepository.GetActiveOrder();
        if(order == null)
        {
            throw new OrderNotFoundException();
        }

        return order;
    }
    
    public async Task UpdateOrderStatus(SupervisorUpdateOrderStatusRequest request)
    {
        switch (request.Type)
        {
            case OrderStatusTypeEnum.Start:
                await supervisorOrderRepository.StartOrder(request.OrderId, DateTime.Now);
                break;
            case OrderStatusTypeEnum.Finish:
                await supervisorOrderRepository.EndOrder(request.OrderId, DateTime.Now);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }
    
    public async Task<Order> SupervisorOrderCreate(SupervisorCreateOrderRequest request)
    {
        // TODO: Sync Server time with org server
        // TODO: Enable socket
        
        bool supervisorCanNotCreateOrder = await settingService.GetAsync<bool>("SupervisorCanNotCreateOrder");
        if (supervisorCanNotCreateOrder)
        {
            throw new SupervisorCanNotCreateOrderException("Supervisor order creation is disabled by Organization.");
        }
        
        Order order = new Order
        {
            Label = request.Label,
            OrderId = request.OrderId,
            BuyerName = request.BuyerName ?? "N/A",
            Country = request.Country ?? "N/A",
            OrderQuantity = request.OrderQuantity,
            HourlyTarget = request.HourlyTarget,
            AssignedAt = DateTime.Now
        };
        
        await supervisorOrderRepository.Create(order);
        await unitOfWork.SaveChangesAsync();
        
        return order;
    }
}
