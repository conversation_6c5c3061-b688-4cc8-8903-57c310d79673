using Domain.CommonResource;
using Domain.Entities;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Response;

namespace LineWebServer.Application.Supervisors.Services;

public interface IItemAcquisitionService
{
    Task<CommonPaginationResource<ItemAcquisitionResource>> Get(GetItemAcquisitionRequest dto);
    Task AcceptApprovedBundleFromCutting(AcceptBundleFromCuttingRequest request);
    Task ReleaseItemAcquisition(ReleaseItemAcquisitionRequest request);
}
