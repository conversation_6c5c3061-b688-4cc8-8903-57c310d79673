using Domain.Entities;
using LineWebServer.Application.Supervisors.DTOs;

namespace LineWebServer.Application.Supervisors.Services;

public interface IItemAcquisitionService
{
    Task<IEnumerable<ItemAcquisition>> Get(GetItemAcquisitionRequest dto);
    Task AcceptApprovedBundleFromCutting(AcceptBundleFromCuttingRequest request);
    Task ReleaseItemAcquisition(ReleaseItemAcquisitionRequest request);
}
