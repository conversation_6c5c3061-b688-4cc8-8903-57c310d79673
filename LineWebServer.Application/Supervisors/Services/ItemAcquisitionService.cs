using Domain;
using Domain.CommonResource;
using Domain.Contracts;
using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Response;
using LineWebServer.Application.Supervisors.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Supervisors.Services;

public sealed class ItemAcquisitionService(IItemAcquisitionRepository itemAcquisitionRepository, ISupervisorService supervisorService, IUnitOfWork unitOfWork, IChannelQueueInterface<PublishToBrokerRequest> brokerQueue, IConfiguration configuration, ILogger<ItemAcquisitionService> logger) : IItemAcquisitionService
{
    public async Task<CommonPaginationResource<ItemAcquisitionResource>> Get(GetItemAcquisitionRequest dto)
    {
        CommonPaginationResource<ItemAcquisition> result = await itemAcquisitionRepository.Get(dto);
        
        return result.ToItemAcquisitionResourcePagination();
    }
    
    public async Task AcceptApprovedBundleFromCutting(AcceptBundleFromCuttingRequest request)
    {
        Order? order = await itemAcquisitionRepository.GetOrderByOrderId(request.OrderId, request.AssignedDate);
        if (order == null)
        {
            throw new OrderNotFoundForRackExchangeException("Order does not exist");
        }

        string? loggedInSupervisorId = await supervisorService.GetLoggedInSupervisorId();
        
        if (loggedInSupervisorId == null)
        {
            throw new LoggedInSupervisorNotFoundForRackExchangeException("Logged in supervisor not found");
        }

        ItemAcquisition itemAcquisition = new ItemAcquisition()
        {
            OrderId = order.Id,
            SupervisorId = loggedInSupervisorId,
            AmountType = request.AmountType,
            Amount = request.Amount,
            BundleSize = request.BundleSize,
            OrgTransitionId = request.Id
        };
        await itemAcquisitionRepository.CreateAsync(itemAcquisition);
        
        await unitOfWork.SaveChangesAsync();        
    }

    public async Task ReleaseItemAcquisition(ReleaseItemAcquisitionRequest request)
    {
        int stationNumber = await itemAcquisitionRepository.ReleaseItemAcquistion(new ReleaseItemAcquisitionRequest
        {
            ItemAcquisitionId = request.ItemAcquisitionId,
            AmountToBeReleased = request.AmountToBeReleased,
        });
        await unitOfWork.SaveChangesAsync();
        
        int floorNo = configuration.GetValue<int?>("LayoutPosition:FloorNo")
                      ?? throw new InvalidOperationException("Floor number not found in configuration.");
        int lineNo = configuration.GetValue<int?>("LayoutPosition:LineNo")
                     ?? throw new InvalidOperationException("Line number not found in configuration.");

        try
        {
            brokerQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{floorNo}/line/{lineNo}/st/{stationNumber}/pts_udt"
            });
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error publishing to broker");
        }
    }
}
