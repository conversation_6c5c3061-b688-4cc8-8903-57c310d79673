using Domain.Entities;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Response;

namespace LineWebServer.Application.Supervisors.Services;

public interface ISupervisorOrderService
{
    Task<IEnumerable<Order>> GetSupervisorOrders(GetAllOrdersRequest request);
    Task<SupervisorOrderActivityResource> GetSupervisorOrderActivity(long orderId);
    Task UpdateOrderStatus(SupervisorUpdateOrderStatusRequest request);
    Task<Order> SupervisorOrderCreate(SupervisorCreateOrderRequest request);
}
