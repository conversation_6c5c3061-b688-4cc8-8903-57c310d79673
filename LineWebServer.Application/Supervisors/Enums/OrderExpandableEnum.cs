using Ardalis.SmartEnum;

namespace LineWebServer.Application.Supervisors.Enums;

public class OrderExpandableEnum(string name, int value) : SmartEnum<OrderExpandableEnum>(name, value)
{
    public static readonly OrderExpandableEnum HourlyOutputs = new OrderExpandableEnum("HourlyOutputs", 1);
    public static readonly OrderExpandableEnum Qcs = new OrderExpandableEnum("Qcs", 2);
    public static readonly OrderExpandableEnum QcAlters = new OrderExpandableEnum("QcAlters", 3);
    public static readonly OrderExpandableEnum QcRejects = new OrderExpandableEnum("QcRejects", 4);
    public static readonly OrderExpandableEnum RackExchanges = new OrderExpandableEnum("RackExchanges", 5);
    
    public static readonly OrderExpandableEnum[] All =
    [
        HourlyOutputs,
        Qcs,
        QcAlters,
        QcRejects,
        RackExchanges
    ];
}
