using Domain.Entities;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.LineMonitors.DTOs;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.LineMonitors.Services;

public class LineMonitorService(ILineMonitorRepository lineMonitorRepository, ILogger<LineMonitorService> logger) : ILineMonitorService
{
    private const int DefaultSmvSeconds = 20;

    public async Task<IEnumerable<WorkerStatsResponse>> GetStationWorkerDetails()
    {
        try
        {
            var today = DateTime.Today;
            var stations = await lineMonitorRepository.GetAllStationsOrderedByNumber();
            var stationWorkers = await lineMonitorRepository.GetCurrentDayActiveWorkers(today);
            
            List<WorkerStatsResponse> response = [];

            foreach (var station in stations)
            {
                // TODO: Cache station worker info
                StationWorker? worker = stationWorkers.FirstOrDefault(w => w.StationId == station.Id);

                if (worker == null)
                {
                    response.Add(LineMonitorResponseBuilder.ToEmptyWorkerStatsResponse(station.Number));
                    continue;
                }
     
                CalculateStationWorkerStatResponseDto workerStat = await CalculateStationWorkerStat(worker, today);
                
                response.Add(worker.ToWorkerStatsResponse(station.Number, workerStat.Green, workerStat.Yellow, workerStat.Red, workerStat.Efficiency));
            }

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting station worker details");
            throw;
        }
    }

    public async Task<CalculateStationWorkerStatResponseDto> CalculateStationWorkerStat(StationWorker worker, DateTime today)
    {
        int green = await lineMonitorRepository.GetWorkerProductionCount(worker.WorkerId, today);
        int yellow = await lineMonitorRepository.GetWorkerAlterCount(worker.WorkerId, today);
        int red = await lineMonitorRepository.GetWorkerRejectCount(worker.WorkerId, today);
        int totalPending = await lineMonitorRepository.GetPendingTaskCount(worker.StationId);

        var workerTimeDetails = await lineMonitorRepository.GetWorkerTimeDetails(worker.WorkerId, today);
        decimal totalMinutesWorked = workerTimeDetails.Sum(w => w.EndedAt == null 
            ? (decimal)(DateTime.Now - w.StartedAt).TotalMinutes 
            : (decimal)(w.EndedAt.Value - w.StartedAt).TotalMinutes);

        decimal totalMinutesProduced = (decimal)(green * (DefaultSmvSeconds / 60.0));
        decimal efficiency = totalMinutesWorked > 0
            ? totalMinutesProduced / totalMinutesWorked * 100
            : 0;
        
        return new CalculateStationWorkerStatResponseDto
        {
            Green = green,
            Yellow = yellow,
            Red = red,
            TotalPending = totalPending,
            Efficiency = decimal.Round(efficiency, 2)
        };
    }
    
    public async Task<OrderDetailsResponse?> GetOrderDetails()
    {
        try
        {
            var today = DateTime.Today;
            var order = await lineMonitorRepository.GetCurrentActiveOrder(today);
            return order?.ToOrderDetailsResponse();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting order details");
            throw;
        }
    }    
    
    public async Task<IEnumerable<HourlyOutputStatsResponse>> GetHourlyStats()
    {
        try
        {
            var today = DateTime.Today;
            var hourlyStats = await lineMonitorRepository.GetHourlyStats(today);
            
            return hourlyStats
                .Select(h => h.ToHourlyOutputStatsResponse())
                .OrderByDescending(h => h.Hour);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting hourly stats");
            throw;
        }
    }

    public async Task<HourlyChartResponse> GetHourlyChart()
    {
        try
        {
            var today = DateTime.Today;
            var yesterday = today.AddDays(-1);
            var fixedHours = Enumerable.Range(9, 8).ToList(); // 9 to 16 (8 hours)
            
            var stats = await lineMonitorRepository.GetHourlyStatsForDays(today, yesterday);
            
            var todayData = stats
                .Where(h => h.CreatedAt.Date == today.Date)
                .GroupBy(h => h.CreatedAt.Hour)
                .ToDictionary(g => g.Key, g => g.Sum(x => x.Production));

            var yesterdayData = stats
                .Where(h => h.CreatedAt.Date == yesterday.Date)
                .GroupBy(h => h.CreatedAt.Hour)
                .ToDictionary(g => g.Key, g => g.Sum(x => x.Production));

            var loggedHours = fixedHours.Select(h => $"{h}:00").ToList();
            var todayProduction = fixedHours.Select(h =>
            {
                todayData.TryGetValue(h, out int value);
                return value;
            }).ToList();

            var yesterdayProduction = fixedHours.Select(h =>
            {
                yesterdayData.TryGetValue(h, out int value);
                return value;
            }).ToList();

            return new HourlyChartResponse
            {
                LoggedHours = loggedHours,
                Today = todayProduction,
                Yesterday = yesterdayProduction
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting hourly chart data");
            throw;
        }
    }   
    
    public async Task<CommonDetailsResponse> GetCommonDetails()
    {
        try
        {
            var today = DateTime.Today;

            int totalWorkers = await lineMonitorRepository.GetTotalWorkersCount();
            int activeWorkers = await lineMonitorRepository.GetActiveWorkersCount();
            
            int totalProductions = await lineMonitorRepository.GetDailyProductionCount(today);
            int totalAlters = await lineMonitorRepository.GetDailyAlterCount(today);
            int totalRejects = await lineMonitorRepository.GetDailyRejectCount(today);

            decimal overallEfficiency = 0;
            var nonDefaultStations = await lineMonitorRepository.GetNonDefaultStations();
            var nonDefaultStationIds = nonDefaultStations.Select(s => s.Id).ToList();
            
            var workerTimeDetails = await lineMonitorRepository.GetWorkersTimeForStations(nonDefaultStationIds, today);
            
            double totalMinutesWorked = workerTimeDetails.Sum(w => w.EndedAt == null 
                ? (DateTime.Now - w.StartedAt).TotalMinutes 
                : (w.EndedAt.Value - w.StartedAt).TotalMinutes);

            double totalMinutesProduced = totalProductions * (DefaultSmvSeconds / 60.0);
            overallEfficiency = totalMinutesWorked > 0
                ? (decimal)(totalMinutesProduced / totalMinutesWorked * 100)
                : 0;

            var order = await lineMonitorRepository.GetCurrentActiveOrder(today);
            int target = order?.OrderQuantity ?? 0;

            return LineMonitorResponseBuilder.ToCommonDetailsResponse(
                totalWorkers,
                activeWorkers,
                totalProductions,
                totalAlters,
                totalRejects,
                overallEfficiency,
                today,
                target,
                totalProductions
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting common details");
            throw;
        }
    }
}