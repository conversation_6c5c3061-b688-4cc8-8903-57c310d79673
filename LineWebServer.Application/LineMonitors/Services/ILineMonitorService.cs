using Domain.Entities;
using LineWebServer.Application.LineMonitors.DTOs;

namespace LineWebServer.Application.LineMonitors.Services;

public interface ILineMonitorService
{
    Task<IEnumerable<WorkerStatsResponse>> GetStationWorkerDetails();
    Task<OrderDetailsResponse?> GetOrderDetails();
    Task<IEnumerable<HourlyOutputStatsResponse>> GetHourlyStats();
    Task<HourlyChartResponse> GetHourlyChart();
    Task<CommonDetailsResponse> GetCommonDetails();
    Task<CalculateStationWorkerStatResponseDto> CalculateStationWorkerStat(StationWorker worker, DateTime today);
}