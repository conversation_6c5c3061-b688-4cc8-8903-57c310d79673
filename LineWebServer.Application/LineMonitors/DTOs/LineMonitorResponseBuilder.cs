using Domain.Entities;
using LineWebServer.Application.LineMonitors.DTOs;

namespace LineWebServer.Application.LineMonitors.Services;

public static class LineMonitorResponseBuilder
{
    public static WorkerStatsResponse ToWorkerStatsResponse(this StationWorker worker, int stationNumber, int green, int yellow, int red, decimal efficiency) 
    {
        return new WorkerStatsResponse
        {
            Id = worker.WorkerId,
            Name = worker.WorkerName,
            StationNumber = stationNumber,
            IsAssigned = true,
            Efficiency = Math.Round(efficiency, 2),
            Yellow = yellow,
            Green = green,
            Red = red
        };
    }

    public static WorkerStatsResponse ToEmptyWorkerStatsResponse(int stationNumber) 
    {
        return new WorkerStatsResponse
        {
            StationNumber = stationNumber,
            IsAssigned = false,
            Efficiency = 0,
            Yellow = 0,
            Green = 0,
            Red = 0
        };
    }

    public static OrderDetailsResponse ToOrderDetailsResponse(this Order order)
    {
        return new OrderDetailsResponse
        {
            Id = order.Id,
            Label = order.Label ?? "N/A",
            BuyerName = order.BuyerName ?? "N/A",
            Country = order.Country ?? "N/A",
            OrderQuantity = order.OrderQuantity,
            HourlyTarget = order.HourlyTarget
        };
    }

    public static HourlyOutputStatsResponse ToHourlyOutputStatsResponse(this HourlyOutput hourly)
    {
        return new HourlyOutputStatsResponse
        {
            Hour = hourly.CreatedAt.Hour,
            Production = hourly.Production,
            Alter = hourly.Alter,
            Reject = hourly.Reject,
            Order = hourly.Order == null ? null : new HourlyOutputOrderResponse
            {
                Id = hourly.Order.Id,
                Label = hourly.Order.Label ?? "N/A"
            }
        };
    }

    public static CommonDetailsResponse ToCommonDetailsResponse(
        int totalWorkers, 
        int activeWorkers, 
        int totalProductions, 
        int totalAlters, 
        int totalRejects, 
        decimal overallEfficiency,
        DateTime date,
        int target,
        int completed)
    {
        var completedPercentage = target > 0 ? (float)completed / target * 100 : 0;
        
        return new CommonDetailsResponse
        {
            TotalWorkers = totalWorkers,
            ActiveWorkers = activeWorkers,
            TotalProduction = totalProductions,
            TotalAlter = totalAlters,
            TotalReject = totalRejects,
            OverallEfficiency = Math.Round(overallEfficiency, 2),
            Date = date,
            Target = target,
            Completed = completed,
            CompletedPercentage = (float)Math.Round(completedPercentage, 2)
        };
    }
}
