using System.Text.Json.Serialization;

namespace LineWebServer.Application.LineMonitors.DTOs;

public class WorkerStatsResponse
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }
    
    [JsonPropertyName("name")]
    public string? Name { get; set; }
    
    [JsonPropertyName("stationNumber")]
    public int StationNumber { get; set; }
    
    [JsonPropertyName("efficiency")]
    public decimal Efficiency { get; set; }
    
    [JsonPropertyName("yellow")]
    public int Yellow { get; set; }
    
    [JsonPropertyName("red")]
    public int Red { get; set; }
    
    [JsonPropertyName("green")]
    public int Green { get; set; }
    
    [JsonPropertyName("isAssigned")]
    public bool IsAssigned { get; set; }
}

public class OrderDetailsResponse
{
    [JsonPropertyName("id")]
    public long Id { get; set; }
    
    [JsonPropertyName("label")]
    public required string Label { get; set; }
    [Json<PERSON>ropertyName("buyerName")]
    public required string BuyerName { get; set; }
    
    [JsonPropertyName("country")]
    public required string Country { get; set; }
    
    [JsonPropertyName("orderQuantity")]
    public int OrderQuantity { get; set; }
    
    [JsonPropertyName("hourlyTarget")]
    public int HourlyTarget { get; set; }

    [JsonPropertyName("isStarted")]
    public bool IsStarted { get; set; }
    
    [JsonPropertyName("assignedAt")]
    public DateTime AssignedAt { get; set; }
    
    [JsonPropertyName("startedAt")]
    public DateTime? StartedAt { get; set; }
}

public class HourlyOutputOrderResponse
{
    public long Id { get; set; }
    public string Label { get; set; } = "N/A";
}

public class HourlyOutputStatsResponse
{
    [JsonPropertyName("id")]
    public long Id { get; set; }

    [JsonPropertyName("orderId")]
    public string OrderLabel { get; set; } = "N/A";
    
    [JsonPropertyName("hour")]
    public int Hour { get; set; }
    
    [JsonPropertyName("production")]
    public int Production { get; set; }
    
    [JsonPropertyName("alter")]
    public int Alter { get; set; }
    
    [JsonPropertyName("reject")]
    public int Reject { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("order")]
    public HourlyOutputOrderResponse? Order { get; set; }
}

public class HourlyChartResponse
{
    [JsonPropertyName("loggedHours")]
    public List<string> LoggedHours { get; set; } = [];
    
    [JsonPropertyName("today")]
    public List<int> Today { get; set; } = [];
    
    [JsonPropertyName("yesterday")]
    public List<int> Yesterday { get; set; } = [];
}

public class CommonDetailsResponse
{
    [JsonPropertyName("totalWorkers")]
    public int TotalWorkers { get; set; }
    
    [JsonPropertyName("activeWorkers")]
    public int ActiveWorkers { get; set; }
    
    [JsonPropertyName("totalProduction")]
    public int TotalProduction { get; set; }

    [JsonPropertyName("totalAlter")] 
    public int TotalAlter { get; set; } = 0;

    [JsonPropertyName("totalReject")] 
    public int TotalReject { get; set; } = 0;
    
    [JsonPropertyName("overallEfficiency")]
    public decimal OverallEfficiency { get; set; }
    
    [JsonPropertyName("date")]
    public DateTime Date { get; set; }

    [JsonPropertyName("target")]
    public int Target { get; set; } = 0;

    [JsonPropertyName("completed")]
    public int Completed { get; set; } = 0;

    [JsonPropertyName("completedPercentage")]
    public float CompletedPercentage { get; set; } = 0;
}