using Domain.CommonResource;
using Domain.Entities;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Repository;

namespace LineWebServer.Application.Contracts.Repository;

public interface IItemAcquisitionRepository
{
    Task<CommonPaginationResource<ItemAcquisition>> Get(GetItemAcquisitionRequest dto);
    Task<ItemAcquisition> CreateAsync(ItemAcquisition itemAcquisition);
    ItemAcquisition Update(ItemAcquisition itemAcquisition);
    Task<Order?> GetOrderByOrderId(string orderId, DateTime targetDate);
    Task<Layout> GetActiveLayout();
    Task<LayoutItem> GetFirstItemOfLayout(long layoutId);
    Task<int> ReleaseItemAcquistion(ReleaseItemAcquisitionRequest requestDto);
    Task<bool> IsExist(RackExchangeRepositoryIsExistDto dto);
    Task<int> GetReleasableQtyFromAcquisition(long itemAcquisitionId);
}
