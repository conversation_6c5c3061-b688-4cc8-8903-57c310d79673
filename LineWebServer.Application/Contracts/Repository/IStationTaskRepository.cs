using Domain.Entities;
using Domain.Enums;

namespace LineWebServer.Application.Contracts.Repository;

public interface IStationTaskRepository
{
    /// <summary>
    /// Get the current task for qc station which have  StationTaskStatusEnum.QualityChecking status
    /// </summary>
    /// <param name="stationId"></param>
    /// <param name="orderId"></param>
    /// <returns></returns>
    Task<StationTask?> GetCurrentTaskOfStation(long stationId, long orderId);
    
    /// <summary>
    /// Get the current task for swing station which have  StationTaskStatusEnum.Processing status
    /// </summary>
    /// <param name="stationId"></param>
    /// <returns></returns>
    Task<StationTask?> GetCurrentTaskOfStation(long stationId);
    Task MoveTaskNextStation(StationTask stationTask, LayoutItem nextLayoutItem);
    Task<StationTask> Create(StationTask stationTask);
    Task HandleOverProductionOfTask(StationTask stationTask, LayoutItem nextLayoutItem, int overProductionQty);
    Task<bool> IsExistByOrderId(long orderId);
}
