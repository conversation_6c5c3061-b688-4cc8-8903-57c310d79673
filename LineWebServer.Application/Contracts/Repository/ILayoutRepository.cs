using Domain.Entities;

namespace LineWebServer.Application.Contracts.Repository;

public interface ILayoutRepository
{
    Task<Layout?> GetActiveLayout();
    Task<IEnumerable<Layout>> GetAllLayouts();
    Task<Layout?> GetLayoutById(long layoutId);
    Task<Layout> ActivateLayout(Layout layout);
    Task<LayoutItem> GetNextLayoutItemOnMoveTaskNextStation(long stationId);
    Task<LayoutItem> GetQcLayoutItem(long layoutId);
    Task<LayoutItem?> GetActiveLayoutLayoutItemByStationId(long stationId);
}
