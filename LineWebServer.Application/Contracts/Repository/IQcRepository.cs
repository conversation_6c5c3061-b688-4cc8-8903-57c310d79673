using Domain.Entities;

namespace LineWebServer.Application.Contracts.Repository;

public interface IQcRepository
{
    Task<Qc> CreateQc(long orderId, long taskId, bool isCompleted = false);
    Task<Layout> GetActiveLayout();
    Task<LayoutItem> GetLayoutItem(long layoutId, long stationId);
    Task<StationWorker?> GetCurrentStationWorker(long stationId);
    Task<StationWorker?> GetTodayLastStationWorker(long stationId);
    void CreateQcAlter(QcAlter qcAlter);
    void CreateQcReject(QcReject qcReject);
    Task<bool> OrderExists(long orderId);
    Task<bool> StationExists(long stationId);
    Task<IList<Order>> GetActiveOrders();
    Task<Order?> GetActiveOrderDetails(long orderId);
    Task<Order?> GetOrderStat(long orderId);
}