using Domain.Entities;

namespace LineWebServer.Application.Contracts.Repository;

public interface ILineMonitorRepository
{
    Task<IEnumerable<Station>> GetAllStationsOrderedByNumber();
    Task<IList<StationWorker>> GetCurrentDayActiveWorkers(DateTime today);
    Task<int> GetWorkerProductionCount(string workerId, DateTime today);
    Task<int> GetWorkerAlterCount(string workerId, DateTime today);
    Task<int> GetWorkerRejectCount(string workerId, DateTime today);
    Task<IEnumerable<StationWorker>> GetWorkerTimeDetails(string workerId, DateTime today);
    Task<Order?> GetCurrentActiveOrder(DateTime today);
    Task<IEnumerable<HourlyOutput>> GetHourlyStats(DateTime today);
    Task<IList<HourlyOutput>> GetHourlyStatsForDays(DateTime today, DateTime yesterday);
    Task<int> GetTotalWorkersCount();
    Task<int> GetActiveWorkersCount();
    Task<int> GetDailyProductionCount(DateTime today);
    Task<int> GetDailyAlterCount(DateTime today);
    Task<int> GetDailyRejectCount(DateTime today);
    Task<IEnumerable<Station>> GetNonDefaultStations();
    Task<IEnumerable<StationWorker>> GetWorkersTimeForStations(IEnumerable<long> stationIds, DateTime today);
    Task<int> GetPendingTaskCount(long stationId);
}