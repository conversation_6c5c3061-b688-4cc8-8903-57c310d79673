using Domain.Entities;

namespace LineWebServer.Application.Contracts.Repository;

public interface IEventRepository
{
    Task Create(Event @event);
    Task HandlePreviouslyWorkerNotLoggedOutScenario(Event @event);
    Task HandleIfWorkerCurrentlyLoggedInAnotherStation(Event @event, Station station);
    Task<StationWorker> HandleCurrentStationLogIn(Event @event, Station station, string workerName, StationWorker? loggedInStationWorker);
    Task<StationWorker?> GetCurrentStationWorker(Event @event, Station station);
}
