using Domain.Entities;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Repository;
using LineWebServer.Application.Supervisors.Enums;

namespace LineWebServer.Application.Contracts;

public interface ISupervisorOrderRepository
{
    Task<IEnumerable<Order>> GetAllAsync(GetAllOrdersRequest ordersRequest, IEnumerable<OrderExpandableEnum>? expands = null);
    Task<Order?> GetOrderById(long orderId, IEnumerable<string>? expands = null);
    Task<Order?> GetActiveOrder();
    void Update(Order order);
    Task Create(Order order);
    Task StartOrder(long orderId, DateTime startedAt);
    Task EndOrder(long orderId, DateTime endedAt);
    Task<bool> IsExist(IsOrderExistDto dto);

}
