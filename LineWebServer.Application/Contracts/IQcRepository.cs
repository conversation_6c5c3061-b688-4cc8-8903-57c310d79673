using Domain.Entities;
using Domain.Enums;

namespace LineWebServer.Application.Contracts;

public interface IQcRepository
{
    Task<Qc> CreateQc(long orderId, string type);
    Task<Layout> GetActiveLayout();
    Task<LayoutItem> GetLayoutItem(long layoutId, long stationId);
    Task<StationWorker?> GetCurrentStationWorker(long stationId);
    Task<StationWorker?> GetTodayLastStationWorker(long stationId);
    void CreateQcAlter(QcAlter qcAlter);
    void CreateQcReject(QcReject qcReject);
    Task<bool> OrderExists(long orderId);
    Task<bool> StationExists(long stationId);
    Task<IList<Order>> GetActiveOrders();
}