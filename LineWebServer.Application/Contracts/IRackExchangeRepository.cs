using Domain.Entities;
using LineWebServer.Application.Cutting.DTOs;
using LineWebServer.Application.Cutting.DTOs.Repositories;

namespace LineWebServer.Application.Contracts;

public interface IRackExchangeRepository
{
    Task<IEnumerable<RackExchange>> Get(GetRackExchangesRequest dto);
    Task<RackExchange> CreateAsync(RackExchange rackExchange);
    RackExchange Update(RackExchange rackExchange);
    Task<Order?> GetOrderByOrderId(string orderId, DateTime targetDate);
    Task<Layout> GetActiveLayout();
    Task<LayoutItem> GetFirstItemOfLayout(long layoutId);
    Task<int> DeliveryFromRack(DeliverBundleFromRackRequest requestDto);
    Task<bool> IsExist(RackExchangeRepositoryIsExistDto dto);
    Task<int> GetAmountDeliverableToLine(long rackExchangeId);
}
