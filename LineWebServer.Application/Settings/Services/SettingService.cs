using System.Globalization;
using System.Net.Http.Json;
using Domain.Entities;
using Domain.Enums;
using Domain.OrgServer.DTOs;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;

namespace LineWebServer.Application.Settings.Services;

public sealed class SettingService(ISettingRepository settingRepository, IUnitOfWork unitOfWork, IHttpClientFactory httpClientFactory): ISettingService
{
    public async Task<T?> GetAsync<T>(string key)
    {
        var targetType = typeof(T);

        Setting? setting = await settingRepository.GetAsync(key);

        if (setting == null || string.IsNullOrWhiteSpace(setting.Value))
        {
            return default;
        }

        object? result;

        if (targetType == typeof(string))
        {
            result = setting.Value;
        }
        else if (targetType == typeof(int))
        {
            result = int.Parse(setting.Value);
        }
        else if (targetType == typeof(bool))
        {
            result = bool.Parse(setting.Value);
        }
        else if (targetType == typeof(DateTime))
        {
            result = DateTime.Parse(setting.Value);
        }
        else
        {
            throw new NotImplementedException();
        }

        return (T?)result;
    }   

    public async Task PutAsync<T>(string key, T value)
    {
        var targetType = typeof(T);
        if (targetType == typeof(string))
        {
            await settingRepository.PutAsync(key, value?.ToString() ?? null);
        }
        else if (targetType == typeof(int))
        {
            await settingRepository.PutAsync(key, int.Parse(value?.ToString() ?? "0").ToString());
        }
        else if (targetType == typeof(bool))
        {
            await settingRepository.PutAsync(key, bool.Parse(value?.ToString() ?? "false").ToString());
        }
        else if (targetType == typeof(DateTime))
        {
            var dateTimeValue = string.IsNullOrWhiteSpace(value?.ToString())
                ? DateTime.Now
                : DateTime.Parse(value.ToString()!);

            await settingRepository.PutAsync(key, dateTimeValue.ToString("o"));
        }
        else
        {
            throw new NotImplementedException();
        }
        
        await unitOfWork.SaveChangesAsync();
    }

    public async Task SyncWithOrgServer()
    {
        HttpClient httpClient = httpClientFactory.CreateClient("OrgServerClient");

        // TODO: In org server implement mqtt to push event to initiate setting sync
        try
        {
            await unitOfWork.BeginTransactionAsync();

            var response = await httpClient.GetAsync("line-server/settings");

            if (response.IsSuccessStatusCode)
            {
                LineServerSettingDto? setting = await response.Content.ReadFromJsonAsync<LineServerSettingDto>();

                if (setting == null)
                {
                    return;
                }

                if (setting.IsSupervisorCanNotCreateOrderSet())
                {
                    await PutAsync(AvailableSettings.SupervisorCanNotCreateOrder, setting.SupervisorCanNotCreateOrder);
                }

            }

            await unitOfWork.CommitAsync();
        }
        catch (Exception)
        {
            await unitOfWork.RollbackAsync();

            throw;
        }
    }
}
