using LineWebServer.Application.Swing.Services;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnNeedAuthJob(ILogger<HandleOnNeedAuthJob> logger, ISwingService swingService)
{
    public async Task Handle(int floor, int line, int station)
    {
        logger.LogInformation("HandleOnNeedAuthJob processing");
        try
        {
            await swingService.PublishStationLoggedAuthData(floor, line, station);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error accepting approved bundle from cutting");
        }
        
        logger.LogInformation("HandleOnNeedAuthJob completed");
    }    
}
