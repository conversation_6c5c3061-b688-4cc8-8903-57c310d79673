using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnProductionSwitchEventJob(IChannelQueueInterface<PublishToBrokerRequest> brokerQueue, ILogger<HandleOnProductionSwitchEventJob> logger, IStationRepository stationRepository, IStationModeRepository stationModeRepository, IStationWorkerRepository stationWorkerRepository, IUnitOfWork unitOfWork, IStationProductionRepository stationProductionRepository)
{
    public async Task Handle(Event @event)
    {
        try
        {
            if(@event.Type != EventTypeEnum.OnProductionSwitch)
            {
                logger.LogError("Invalid event type: {@event.Type}", @event.Type);

                return;
            }
            logger.LogInformation("HandleOnProductionSwitchEventJob processing");

            Station? station = await stationRepository.GetStationByNumber(@event.Station);
            if (station == null)
            {
                logger.LogError("Station not found");
                brokerQueue.Enqueue(new PublishToBrokerRequest
                {
                    Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
                });

                return;
            }

            
            StationWorker? stationWorker = await stationWorkerRepository.GetLoggedStationWorkerById(station.Id);
            if (stationWorker == null)
            {
                logger.LogError("Station Worker not found");
                brokerQueue.Enqueue(new PublishToBrokerRequest
                {
                    Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
                });

                return;
            }
            
            
            StationMode? stationMode = await stationModeRepository.GetCurrentByStation(station.Id);
            
            if (stationMode == null)
            {
                stationMode = new StationMode
                {
                    StationId = station.Id,
                    Mode = StationModeEnum.Production,
                    StartedAt = DateTime.Now,
                };
                await stationModeRepository.Create(stationMode);
            }
            
            if(stationMode.Mode == StationModeEnum.Maintenance)
            {
                brokerQueue.Enqueue(new PublishToBrokerRequest
                {
                    Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/beep_now"
                });

                await unitOfWork.SaveChangesAsync();
                logger.LogInformation("Station currently maintenance mode");
                return;
            }
            
            
            string stationProductionType = stationMode.Mode switch
            {
                StationModeEnum.Alter => ProductionTypeEnum.Alter,
                StationModeEnum.Production => ProductionTypeEnum.Production,
                _ => throw new InvalidOperationException("Inappropriate station mode")
            };
            
            
            await stationProductionRepository.Create(new StationProduction
            {
                StationId = station.Id,
                CreatedAt = DateTime.Now,
                StationWorkerId = stationWorker.Id,
                WorkerId = stationWorker.WorkerId,
                Type = stationProductionType,
                StationType = station.Type
            });
            await unitOfWork.SaveChangesAsync();
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling event");
        }
    }
}