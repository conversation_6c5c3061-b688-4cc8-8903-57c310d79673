using LineWebServer.Application.Settings.Services;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleSettingUpdatedOnOrgServer(ISettingService settingService, ILogger<HandleSettingUpdatedOnOrgServer> logger)
{
    public async Task Handle()
    {
        try
        {
            logger.LogInformation("Processing HandleSettingUpdatedOnOrgServer event");
            await settingService.SyncWithOrgServer();
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling HandleSettingUpdatedOnOrgServer");
        }
    }
}
