using LineWebServer.Application.Swing.Services;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnNeedStModeJob(ILogger<HandleOnNeedAuthJob> logger, ISwingService swingService)
{
    public async Task Handle(int floor, int line, int station)
    {
        try
        {
            await swingService.PublishStationModeData(floor, line, station);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error accepting approved bundle from cutting");
        }        
    }    
}
