using System.Net.Http.Json;
using System.Text.Json;
using Domain;
using Domain.Contracts;
using Domain.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnStatDetailsEventJob(IChannelQueueInterface<PublishToBrokerRequest> brokerQueue, ILogger<HandleOnStatDetailsEventJob> logger, IConfiguration configuration)
{
    public async Task Handle(Event @event)
    {
        try
        {
            logger.LogInformation("HandleOnStatDetailsEventJob processing");
            string? orgServerBaseUrl = configuration["OrgServer:BaseUrl"];

            if (orgServerBaseUrl != null)
            {
                var client = new HttpClient();
                client.BaseAddress = new Uri(orgServerBaseUrl);
                client.Timeout = TimeSpan.FromSeconds(10);
                HttpResponseMessage response = await client.GetAsync($"line-server/workers/{@event.Payload}/all-time-stats");
                if (response.IsSuccessStatusCode)
                {
                    var stat = await response.Content.ReadFromJsonAsync<JsonDocument>();
                    if (stat == null)
                    {
                        return;
                    }
                    
                    int green = stat.RootElement.TryGetProperty("Green", out var greenProp) ? greenProp.GetInt32() : 0;
                    int red = stat.RootElement.TryGetProperty("Red", out var redProp) ? redProp.GetInt32() : 0;
                    int yellow = stat.RootElement.TryGetProperty("Yellow", out var yellowProp) ? yellowProp.GetInt32() : 0;
                    decimal eff = stat.RootElement.TryGetProperty("Eff", out var effProp) ? effProp.GetDecimal() : 0;
                    
                    brokerQueue.Enqueue(new PublishToBrokerRequest
                    {
                        Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/ats_details",
                        Payload = $"{eff},{red},{green},{yellow}"
                    });                    
                }
            }
            logger.LogInformation("Processing complete");
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling event");
        }
    }
}