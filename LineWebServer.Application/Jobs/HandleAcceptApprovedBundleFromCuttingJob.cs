using System.Net.Http.Json;
using Domain.Enums;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.Services;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleAcceptApprovedBundleFromCuttingJob(IItemAcquisitionService itemAcquisitionService, AcceptBundleFromCuttingRequestValidator validator, ILogger<HandleAcceptApprovedBundleFromCuttingJob> logger, IHttpClientFactory clientFactory)
{
    public async Task Handle(long orgRackExchangeId, string orderId)
    {
        try
        {
            var httpClient = clientFactory.CreateClient(HttpClientTypeEnum.OrgServer);
            HttpResponseMessage response = await httpClient.GetAsync($"line-server/rack-exchanges/{orgRackExchangeId}?orderId={orderId}");
            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Failed to retrieve rack exchange data. Status: {StatusCode}, Response: {ResponseBody}", response.StatusCode, response);
                return;
            }

            var request = await response.Content.ReadFromJsonAsync<AcceptBundleFromCuttingRequest>();
            if (request == null)
            {
                logger.LogError("Failed to deserialize rack exchange data");                
                return;
            }

            var result = await validator.ValidateAsync(request);
            if (!result.IsValid)
            {
                logger.LogError("Invalid request: {@errors}", result.Errors);
                return;
            }

            await itemAcquisitionService.AcceptApprovedBundleFromCutting(request);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error accepting approved bundle from cutting");
        }        
    }    
}
