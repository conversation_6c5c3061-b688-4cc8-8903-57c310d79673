using Domain.Entities;
using LineWebServer.Application.Swing.Services;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleStationProductionSwitchEvent(IStationProductionService stationProductionService, ILogger<HandleStationProductionSwitchEvent> logger)
{
    public async Task Handle(Event @event)
    {
        try
        {
            await stationProductionService.HandleSwitchingFromStation(@event);         
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling HandleStationProductionSwitchEvent");
        }
    }
}