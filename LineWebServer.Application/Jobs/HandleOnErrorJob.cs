using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnErrorJob(ILogger<HandleOnErrorJob> logger)
{
    public void Handle(int floor, int line, int station, string payload)
    {
        try
        {
            logger.LogCritical("Floor:{floor}, Line:{line}, Station:{station}, Error: {payload}", floor, line, station, payload);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error accepting approved bundle from cutting");
        }        
    }    
}
