using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Contracts.Repository;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class HandleOnAlterModeEventJob(ILogger<HandleOnAlterModeEventJob> logger, IStationRepository stationRepository, IStationModeRepository stationModeRepository, IUnitOfWork unitOfWork)
{
    public async Task Handle(Event @event)
    {
        try
        {
            if(@event.Type != EventTypeEnum.OnAlterMode)
            {
                logger.LogError("Invalid event type: {@event.Type}", @event.Type);

                return;
            }
            
            logger.LogInformation("HandleOnAlterModeEventJob processing");

            Station? station = await stationRepository.GetStationByNumber(@event.Station);
            if (station == null)
            {
                logger.LogError("Station not found");
                return;
            }
            
            station.CurrentMode = StationModeEnum.Alter;
            stationRepository.Update(station);
            
            StationMode? stationMode = await stationModeRepository.GetCurrentByStation(station.Id);
            if (stationMode == null)
            {
                await stationModeRepository.Create(new StationMode
                {
                    StationId = station.Id,
                    Mode = StationModeEnum.Alter,
                    StartedAt = DateTime.Now,
                });
                await unitOfWork.SaveChangesAsync();

                return;
            }

            if (stationMode.Mode == StationModeEnum.Alter)
            {
                return;
            }
            
            stationMode.EndedAt = DateTime.Now;
            stationModeRepository.Update(stationMode);

            await stationModeRepository.Create(new StationMode
            {
                StationId = station.Id,
                Mode = StationModeEnum.Alter,
                StartedAt = DateTime.Now,
            });
 
            await unitOfWork.SaveChangesAsync();
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling event");
        }
    }
}