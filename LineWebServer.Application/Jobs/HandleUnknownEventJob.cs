using Domain.Entities;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Jobs;

public class HandleUnknownEventJob(ILogger<HandleUnknownEventJob> logger)
{
    public async Task Handle(Event @event)
    {
        try
        {
            await Task.Delay(100);
            logger.LogInformation("HandleUnknownEventJob processing");
            logger.LogWarning("Unknown event type: {@event.Type}", @event);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling event");
        }
    }
}