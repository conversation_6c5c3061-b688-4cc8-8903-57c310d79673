using LineWebServer.Application.Contracts.Repository;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Jobs;

public class ProcessHourlyOutput(IHourlyOutputRepository hourlyOutputRepository, IUnitOfWork unitOfWork, ILogger<ProcessHourlyOutput> logger)
{
    public async Task Handle()
    {
        logger.LogInformation("Processing ProcessHourlyOutput event");
        
        await hourlyOutputRepository.CreateOnHourly();
        
        await unitOfWork.SaveChangesAsync();
        
        logger.LogInformation("Processing ProcessHourlyOutput event completed");
    }
}
