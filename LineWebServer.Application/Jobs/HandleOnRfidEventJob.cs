using Domain;
using Domain.Contracts;
using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;
using LineWebServer.Application.Contracts.Repository;
using LineWebServer.Application.LineMonitors.DTOs;
using LineWebServer.Application.LineMonitors.Services;

namespace LineWebServer.Application.Jobs;

public class HandleOnRfidEventJob(
    ILogger<HandleOnRfidEventJob> logger,
    IChannelQueueInterface<PublishToBrokerRequest> mqttPublishQueue,
    IEventRepository eventRepository,
    IStationRepository stationRepository,
    ILineMonitorService lineMonitorService,
    ILayoutRepository layoutRepository,
    IHttpClientFactory httpClientFactory)
{
    public async Task Handle(Event @event)
    {
        try
        {
            if (@event.Type != EventTypeEnum.OnRfidSet)
            {
                logger.LogError("Invalid event type: {@event.Type}", @event.Type);

                return;
            }

            logger.LogInformation("HandleOnRfidEventJob processing");

            if (@event.Payload is null)
            {
                logger.LogError("Event payload is null");

                return;
            }

            Station? station = await stationRepository.GetStationByNumber(@event.Station);

            if (station == null)
            {
                logger.LogError("Station not found");

                return;
            }


            await eventRepository.HandlePreviouslyWorkerNotLoggedOutScenario(@event);

            await eventRepository.HandleIfWorkerCurrentlyLoggedInAnotherStation(@event, station);

            StationWorker? currentStationWorker = await eventRepository.GetCurrentStationWorker(@event, station);
            string workerName = "N/A";

            if (currentStationWorker == null)
            {
                workerName = await GetWorkerName(@event.Payload) ?? "N/A";
            }

            StationWorker worker = await eventRepository.HandleCurrentStationLogIn(@event, station, workerName, currentStationWorker);

            await HandleEchoWorkerDataToOperatorPanel(@event, worker);
            
            await SendWorkerLoginToOrgServer(@event.Payload);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error handling event");
        }
    }

    private async Task<string?> GetWorkerName(string workerId)
    {
        HttpClient httpClient = httpClientFactory.CreateClient("OrgServerClient");

        try
        {
            HttpResponseMessage response = await httpClient.GetAsync($"line-server/workers/{workerId}");

            if (response.IsSuccessStatusCode)
            {
                var workerData = await response.Content.ReadFromJsonAsync<JsonDocument>();
                    
                if (workerData != null && workerData.RootElement.TryGetProperty("Name", out var nameProperty))
                {
                    return nameProperty.GetString();
                }
            }
            else
            {
                logger.LogWarning("Failed to retrieve worker data. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving worker data from org server");
        }

        return null;
    }

    private async Task SendWorkerLoginToOrgServer(string workerId)
    {
        HttpClient httpClient = httpClientFactory.CreateClient("OrgServerClient");

        try
        {
            HttpResponseMessage response = await httpClient.PutAsync(new Uri($"line-server/workers/{workerId}/worker-login", UriKind.Relative), null);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("Failed to retrieve worker data. Status: {StatusCode}", response.StatusCode);
                
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending worker login to org server");
        }

    }


    private async Task HandleEchoWorkerDataToOperatorPanel(Event @event, StationWorker stationWorker)
    {
        if (stationWorker.EndedAt == null)
        {
            CalculateStationWorkerStatResponseDto stationWorkerStat = await lineMonitorService.CalculateStationWorkerStat(stationWorker, DateTime.Today);
            LayoutItem? layoutItem = await layoutRepository.GetActiveLayoutLayoutItemByStationId(stationWorker.StationId);
            mqttPublishQueue.Enqueue(new PublishToBrokerRequest
            {
                Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/all_data",
                Payload = $"{layoutItem?.Type ?? "N/A"},{stationWorker.WorkerName},{stationWorkerStat.Efficiency},{stationWorkerStat.Red},{stationWorkerStat.Green},{stationWorkerStat.Yellow},{stationWorkerStat.TotalPending}"
            });

            return;
        }

        mqttPublishQueue.Enqueue(new PublishToBrokerRequest
        {
            Topic = $"floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/reset",
        });
    }
}
