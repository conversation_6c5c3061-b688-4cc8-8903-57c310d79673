using Domain.Entities;
using Domain.Enums;
using LineWebServer.Application.Contracts;
using LineWebServer.Application.Qcs.DTOs;
using LineWebServer.Application.Qcs.Exceptions;
using Microsoft.Extensions.Logging;

namespace LineWebServer.Application.Qcs.Services;

public class QcService(IQcRepository qcRepository, ILogger<QcService> logger, IUnitOfWork unitOfWork) : IQcService
{
    public async Task<Qc> ApplyQc(ApplyQcRequest request)
    {
        var qc = await qcRepository.CreateQc(request.OrderId, request.Type);
        await unitOfWork.SaveChangesAsync();
        logger.LogInformation("Created QC record with ID {QcId} for order {OrderId}", qc.Id, request.OrderId);
        
        if (request.Type == QcTypeEnum.Ok)
        {
            return qc;
        }

        var layout = await qcRepository.GetActiveLayout();
        if (layout == null)
        {
            throw new ActiveLayoutNotFoundException("No active layout found for QC operation");
        }

        try
        {
            foreach (var tagRequest in request.Tags ?? Enumerable.Empty<QcTagRequest>())
            {
                await ProcessQcTag(request, qc, layout, tagRequest);
            }
            
            await unitOfWork.SaveChangesAsync();
            logger.LogInformation("Successfully processed QC {Type} for order {OrderId}", request.Type, request.OrderId);
            
            return qc;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing QC {Type} for order {OrderId}", request.Type, request.OrderId);
            throw;
        }
    }

    private async Task ProcessQcTag(ApplyQcRequest request, Qc qc, Layout layout, QcTagRequest tagRequest)
    {
        var layoutItem = await qcRepository.GetLayoutItem(layout.Id, tagRequest.StationId);
        var stationWorker = await qcRepository.GetCurrentStationWorker(tagRequest.StationId) 
                          ?? await qcRepository.GetTodayLastStationWorker(tagRequest.StationId);

        if (stationWorker == null)
        {
            logger.LogWarning("Station worker not found for station id: {StationId} at {Now}", 
                tagRequest.StationId, DateTime.Now);
        }

        // TODO: Need somehow figureout based on workerId
        string workerId = stationWorker?.WorkerId ?? $"N/A:{tagRequest.StationId}";

        if (request.Type == QcTypeEnum.Alter)
        {
            qcRepository.CreateQcAlter(new QcAlter
            {
                OrderId = request.OrderId,
                QcId = qc.Id,
                Type = layoutItem.Type,
                WorkerId = workerId
            });
        }
        else
        {
            qcRepository.CreateQcReject(new QcReject
            {
                OrderId = request.OrderId,
                QcId = qc.Id,
                Type = layoutItem.Type,
                WorkerId = workerId
            });
        }
    }
    
    public async Task<IList<Order>> GetActiveOrders()
    {
        return await qcRepository.GetActiveOrders();
    }
}