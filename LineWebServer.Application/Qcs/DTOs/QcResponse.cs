using System.Text.Json.Serialization;
using Domain.Entities;

namespace LineWebServer.Application.Qcs.DTOs;

public class QcResponse
{
    [JsonPropertyName("id")]
    public long Id { get; set; }
    
    [JsonPropertyName("order_id")]
    public long OrderId { get; set; }
    
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;
    
    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }
}

public static class QcResponseBuilder
{
    public static QcResponse ToQcResponse(this Qc qc)
    {
        return new QcResponse
        {
            Id = qc.Id,
            OrderId = qc.OrderId,
            Type = qc.Type,
            CreatedAt = qc.CreatedAt
        };
    }
}