using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Domain.Enums;
using Domain.OrgServer.DTOs;
using LineWebServer.Application.Supervisors.DTOs;

namespace OrgServer.Api.Controllers;

[ApiController]
[Route("line-server")]
public class LineServerController : ControllerBase
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<LineServerController> _logger;
    private readonly List<JsonElement> _workerNames;
    private readonly List<JsonElement> _workerStats;
    private readonly List<JsonElement> _rackExchanges;

    public LineServerController(IWebHostEnvironment environment, ILogger<LineServerController> logger)
    {
        _environment = environment;
        _logger = logger;
        _workerNames = LoadWorkersFromJson();
        _workerStats = LoadWorkersStatFromJson();
        _rackExchanges = LoadRackExchangesFromJson();
    }

    private List<JsonElement> LoadWorkersFromJson()
    {
        try
        {
            string filePath = Path.Combine(_environment.ContentRootPath, "Data", "workers.json");

            if (System.IO.File.Exists(filePath))
            {
                string jsonContent = System.IO.File.ReadAllText(filePath);

                return JsonSerializer.Deserialize<List<JsonElement>>(jsonContent) ?? new List<JsonElement>();
            }

            _logger.LogWarning("Workers JSON file not found at {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading workers from JSON file");
        }

        return new List<JsonElement>();
    }

    private List<JsonElement> LoadWorkersStatFromJson()
    {
        try
        {
            string filePath = Path.Combine(_environment.ContentRootPath, "Data", "workers_stat.json");

            if (System.IO.File.Exists(filePath))
            {
                string jsonContent = System.IO.File.ReadAllText(filePath);

                return JsonSerializer.Deserialize<List<JsonElement>>(jsonContent) ?? new List<JsonElement>();
            }

            _logger.LogWarning("Workers JSON file not found at {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading workers from JSON file");
        }

        return new List<JsonElement>();
    }

    private List<JsonElement> LoadRackExchangesFromJson()
    {
        try
        {
            string filePath = Path.Combine(_environment.ContentRootPath, "Data", "rack_exchanges.json");

            if (System.IO.File.Exists(filePath))
            {
                string jsonContent = System.IO.File.ReadAllText(filePath);

                return JsonSerializer.Deserialize<List<JsonElement>>(jsonContent) ?? new List<JsonElement>();
            }

            _logger.LogWarning("Workers JSON file not found at {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading workers from JSON file");
        }

        return new List<JsonElement>();
    }

    [HttpGet("workers/{workerId}")]
    public IActionResult Details(string workerId)
    {
        var worker = _workerNames.FirstOrDefault(w =>
            w.TryGetProperty("WorkerId", out var idProp) && idProp.GetString() == workerId
        );

        if (worker.ValueKind == JsonValueKind.Undefined)
        {
            return NotFound();
        }

        return Ok(worker);
    }

    [HttpGet("workers/{workerId}/all-time-stats")]
    public IActionResult WorkerStatAllTime(string workerId)
    {
        var worker = _workerStats.FirstOrDefault(w =>
            w.TryGetProperty("WorkerId", out var idProp) && idProp.GetString() == workerId
        );

        if (worker.ValueKind == JsonValueKind.Undefined)
        {
            return NotFound();
        }

        return Ok(worker);
    }

    [HttpPut("workers/{workerId}/worker-login")]
    public IActionResult LoginWorker([FromRoute]string workerId)
    {
        // TODO: Implement line server authentication system. maybe line server will create request to onboard. then org server will approve the request
        return Ok();
    }

    [HttpGet("settings")]
    public IActionResult GetSettings()
    {
        return Ok(new LineServerSettingDto()
        {
            SupervisorCanNotCreateOrder = false
        });
    }

    [HttpGet("rack-exchanges/{rackExchangeId}")]
    public IActionResult RackExchangeDetails(long rackExchangeId, [FromQuery] string? orderId)
    {
        var rackExchange = _rackExchanges.FirstOrDefault(r =>
            r.TryGetProperty("Id", out var idProp) && idProp.GetInt64() == rackExchangeId
        );

        if (rackExchange.ValueKind == JsonValueKind.Undefined)
        {
            return NotFound();
        }

        return Ok(new AcceptBundleFromCuttingRequest
        {
            Id = rackExchange.GetProperty("Id").GetInt64(),
            OrderId = !string.IsNullOrEmpty(orderId) ? orderId : rackExchange.GetProperty("OrderId").GetInt32().ToString(),
            BundleSize = rackExchange.GetProperty("BundleSize").GetInt16(),
            AssignedDate = DateTime.Today,
            AmountType = rackExchange.GetProperty("AmountType").GetString() ?? ItemAcquisitionTypeEnum.Bundle,
            Amount = rackExchange.GetProperty("Amount").GetInt32()
        });
    }
}
