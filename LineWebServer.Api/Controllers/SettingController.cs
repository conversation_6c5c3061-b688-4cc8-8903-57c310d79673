using Domain.Contracts.Services;
using Domain.Entities;
using LineWebServer.Application.Layouts.DTOs;
using LineWebServer.Application.Layouts.Exceptions;
using LineWebServer.Application.Settings.Services;
using Microsoft.AspNetCore.Mvc;

namespace LineWebServer.Controllers;

[ApiController]
[Route("settings")]
public class SettingController(ISettingService settingService): ControllerBase
{
    [HttpGet("sync-with-org-server")]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> SyncWithOrgServer()
    {
        await settingService.SyncWithOrgServer();

        return Ok();
    }
}
