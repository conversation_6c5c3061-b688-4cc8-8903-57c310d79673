using Domain.Contracts.Services;
using Domain.Entities;
using LineWebServer.Application.Layouts.DTOs;
using LineWebServer.Application.Layouts.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace LineWebServer.Controllers;

[ApiController]
[Route("layout")]
public class LayoutController(ILayoutService layoutService): ControllerBase
{
    [HttpGet("active-layout")]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<LayoutResponse>> ActiveLayout()
    {
        Layout? layout = await layoutService.ActiveLayout();
        if (layout == null)
        {
            return NotFound();
        }

        return Ok(layout.ToActiveLayoutResponse());
    }
    
    [HttpGet("all-layouts")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<LayoutResponse>>> AllLayouts()
    {
        IEnumerable<Layout> layouts = await layoutService.AllLayouts();
        
        return Ok(layouts.Select(l => l.ToListLayoutResponse()));
    }
    
    [HttpPut("{layoutId:long}/activate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult> ActivateLayout([FromRoute]long layoutId)
    {
        try
        {
            Layout layout = await layoutService.ActivateLayout(layoutId);

            return Ok(layout.ToActiveLayoutResponse());
        }
        catch (LayoutNotFoundException)
        {
            return NotFound();
        }
    }
    
    [HttpGet("{layoutId:long}/details")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<LayoutResponse>> LayoutDetails([FromRoute]long layoutId)
    {
        try
        {
            Layout layout = await layoutService.LayoutDetails(layoutId);

            return Ok(layout.ToActiveLayoutResponse());
        }
        catch (LayoutNotFoundException)
        {
            return NotFound();
        }
    }
}
