using System.Collections;
using LineWebServer.Application.Cutting.DTOs;
using LineWebServer.Application.Cutting.Services;
using LineWebServer.Application.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace LineWebServer.Controllers;

[ApiController]
[Route("cutting/rack-exchanges")]
public class CuttingController(ICuttingService cuttingService, ILogger<CuttingController> logger) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] GetRackExchangesRequest request)
    {
        try
        {
            var rackExchanges = await cuttingService.Get(request);

            return Ok(rackExchanges);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor orders");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    [HttpPost]
    public async Task<IActionResult> Deliver([FromBody] DeliverBundleFromRackRequest request, DeliverBundleFromRackRequestValidator validator)
    {
        var validationResult = await validator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(new ProblemDetails().RespondWithValidationErrors(validationResult.Errors));
        }

        try
        {
            await cuttingService.DeliverBundleFromRack(request);

            return Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor orders");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }

}
