using LineWebServer.Application.LineMonitors.DTOs;
using LineWebServer.Application.LineMonitors.Services;
using Microsoft.AspNetCore.Mvc;

namespace LineWebServer.Controllers;

[ApiController]
[Route("line-monitor")]
public class LineMonitorController(ILineMonitorService lineMonitorService, ILogger<LineMonitorController> logger) : ControllerBase
{
    [HttpGet]
    [Route("station-worker-details")]
    [ProducesResponseType(typeof(IEnumerable<WorkerStatsResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<WorkerStatsResponse>>> StationWorkerDetails()
    {
        try
        {
            var result = await lineMonitorService.GetStationWorkerDetails();
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting station worker details");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpGet]
    [Route("order-details")]
    [ProducesResponseType(typeof(OrderDetailsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<OrderDetailsResponse>> OrderDetails()
    {
        try
        {
            var result = await lineMonitorService.GetOrderDetails();
            if (result == null)
            {
                return NotFound("Today assigned order not found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting order details");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpGet]
    [Route("hourly-details")]
    [ProducesResponseType(typeof(IEnumerable<HourlyOutputStatsResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HourlyOutputStatsResponse>>> HourlyDetails()
    {
        try
        {
            var result = await lineMonitorService.GetHourlyStats();
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting hourly details");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpGet]
    [Route("hourly-details-chart")]
    [ProducesResponseType(typeof(HourlyChartResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<HourlyChartResponse>> HourlyDetailsChart()
    {
        try
        {
            var result = await lineMonitorService.GetHourlyChart();
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting hourly details chart");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpGet]
    [Route("common-details")]
    [ProducesResponseType(typeof(CommonDetailsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CommonDetailsResponse>> CommonDetails()
    {
        try
        {
            var result = await lineMonitorService.GetCommonDetails();
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting common details");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
}
