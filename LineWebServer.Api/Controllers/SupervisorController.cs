using Domain.CommonResource;
using FluentValidation.Results;
using LineWebServer.Application.Extensions;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Supervisors.DTOs.Response;
using LineWebServer.Application.Supervisors.Exceptions;
using LineWebServer.Application.Supervisors.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace LineWebServer.Controllers;

[ApiController]
[Route("supervisor")]
public class SupervisorController(ISupervisorOrderService supervisorOrderService, IItemAcquisitionService itemAcquisitionService, ILogger<SupervisorController> logger) : ControllerBase {
    [HttpGet("orders")]
    [ProducesResponseType(typeof(CommonPaginationResource<OrderResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<CommonPaginationResource<OrderResponse>>> GetSupervisorOrders([FromQuery] GetAllOrdersRequest request)
    {
        try
        {
            CommonPaginationResource<OrderResponse> result = await supervisorOrderService.GetSupervisorOrders(request);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor orders");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    [HttpGet("orders/{orderId}/activities")]
    [ProducesResponseType(typeof(SupervisorOrderActivityResource), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<SupervisorOrderActivityResource>> GetSupervisorOrderActivities([FromRoute] long orderId)
    {
        try
        {
            SupervisorOrderActivityResource result = await supervisorOrderService.GetSupervisorOrderActivity(orderId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor order activities");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpPut("orders/status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult> UpdateOrderStatus([FromBody] SupervisorUpdateOrderStatusRequest request,
        UpdateOrderStatusRequestValidator validator)
    {
        var validationResult = await validator.ValidateAsync(request);

        if (!validationResult.IsValid)
        {
            return BadRequest(new ProblemDetails().RespondWithValidationErrors(validationResult.Errors));
        }

        try
        {
            await supervisorOrderService.UpdateOrderStatus(request);

            return Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating order status");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    private IDictionary<string, string[]> ToDictionary(IList<ValidationFailure> failures)
    {
        return failures
            .GroupBy(f => f.PropertyName)
            .ToDictionary(
                g => g.Key,
                g => g.Select(f => f.ErrorMessage).ToArray()
            );
    }

    [HttpPost("orders")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult> CreateOrder([FromBody] SupervisorCreateOrderRequest request, CreateOrderRequestValidator validator)
    {
        // TODO: supervisor Prevent from creating duplicate orderId or autogenerate the orderId for app
        // TODO: supervisor order list add filter and pagination, default should be descending order by date
        // TODO: supervisor item acquisition filter should be for specific order and add pagination
        // TODO: supervisor should be able to create item acquisition if cutting mode is off
        // TODO: need supervisor activity api
        // TODO: supervisor order add filter: http://localhost:5027/supervisor/orders?page=1&perPage=10&order=desc&assignedDate=2025-46-04   
        // TODO: supervisor item acquisitions add filter http://localhost:5027/supervisor/item-acquisitions?page=1&perPage=10&order=desc

        var validationResult = await validator.ValidateAsync(request);
        
        if (!validationResult.IsValid)
        {
            return ValidationProblem(new ModelStateDictionary().RespondForValidationProblem(validationResult.Errors));
        }
        
        try
        {
            var order = await supervisorOrderService.SupervisorOrderCreate(request);

            return CreatedAtAction(nameof(GetSupervisorOrders), new { orderId = order.Id }, order.ToOrderResponse());
        }
        catch (SupervisorCanNotCreateOrderException ex)
        {
            logger.LogWarning(ex, "Supervisor can not create order");

            return BadRequest(new ProblemDetails
            {
                Title = "Bad Request",
                Detail = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating order");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpGet("item-acquisitions")]
    public async Task<ActionResult<CommonPaginationResource<ItemAcquisitionResource>>> GetItemAcquisitions([FromQuery] GetItemAcquisitionRequest request)
    {
        try
        {
            CommonPaginationResource<ItemAcquisitionResource> itemAcquisitions = await itemAcquisitionService.Get(request);

            return Ok(itemAcquisitions);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor orders");

            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    [HttpPost("item-acquisitions/release")]
    public async Task<IActionResult> Release([FromBody] ReleaseItemAcquisitionRequest request, DeliverBundleFromRackRequestValidator validator)
    {
        var validationResult = await validator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(new ProblemDetails().RespondWithValidationErrors(validationResult.Errors));
        }
        

        try
        {
            await itemAcquisitionService.ReleaseItemAcquisition(request);
        
            return Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting supervisor orders");
        
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    
    // TODO: If there is no any active layout then in supervisor panel show layout is not configured correctly from org server

}
