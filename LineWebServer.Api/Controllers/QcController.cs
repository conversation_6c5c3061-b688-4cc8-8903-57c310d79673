using Domain.Entities;
using LineWebServer.Application.Extensions;
using LineWebServer.Application.Supervisors.DTOs;
using LineWebServer.Application.Swing.DTOs;
using LineWebServer.Application.Swing.Exceptions;
using LineWebServer.Application.Swing.Services;
using Microsoft.AspNetCore.Mvc;

namespace LineWebServer.Controllers;

[ApiController]
[Route("qc")]
public class QcController(IQcService qcService, ILogger<QcController> logger) : ControllerBase
{
    
    [HttpGet("active-orders")]
    public async Task<ActionResult<IEnumerable<Order>>> GetActiveOrders()
    {
        try
        {
            var result = await qcService.GetActiveOrders();
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting active orders");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    
    [HttpGet("active-orders/{orderId}")]
    public async Task<ActionResult<ToGetActiveOrderDetailsResource?>> GetActiveOrderDetails(long orderId)
    {
        try
        {
            var result = await qcService.GetActiveOrderDetails(orderId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting active orders");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
    
    [HttpPost("apply")]
    [ProducesResponseType(typeof(QcApplyResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Apply([FromBody]ApplyQcRequest request, ApplyQcRequestValidator validator)
    {
        // TODO: Check QC Is logged in
        // TODO: Check how many actually pending for the QC
        
        var validationResult = await validator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(new ProblemDetails().RespondWithValidationErrors(validationResult.Errors));
        }
        
        try
        {
            var result = await qcService.ApplyQc(request);
            return Ok(result);
        }
        catch (QcValidationException ex)
        {
            logger.LogWarning(ex, "Validation error in QC apply request");
            return BadRequest(ex.Message);
        }
        catch (ActiveLayoutNotFoundException ex)
        {
            logger.LogError(ex, "No active layout found for QC operation");
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error applying QC");
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
}
