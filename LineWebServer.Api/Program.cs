using LineWebServer.Application.Extensions;
using LineWebServer.Extensions;
using LineWebServer.Infra.Extensions;

namespace LineWebServer;

public static class Program
{
    public static async Task<int> Main(string[] args)
    {
        bool isCliApp = args.Length > 0 && args.Any(a => a.Contains("--cli"));  

        WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

        builder.Services.AddServices(builder.Configuration);
        
        builder.Services.AddProblemDetailsService(builder.Configuration);
        
        builder.Services.AddCorsService(builder.Configuration);
        
        builder.Services.AddServicesFromInfra(builder.Configuration);
     
        builder.Services.AddValidatorsFromApplication();

        builder.Services.RegisterServices();
        
        builder.Services.AddCommands();

        builder.Services.AddMqttBackgroundService(builder.Configuration, isCliApp);
        
        builder.Services.AddHangfireServiceFromApplication(builder.Configuration, isCliApp);
        
        builder.Services.AddHttpClientsFromApplication(builder.Configuration);
        
        
        WebApplication app = builder.Build();

        app.ConfigureApp();
        
        app.ConfigureAppForHangfire(isCliApp);

        if (isCliApp)
        {
            return await app.RunCliApplicationAsync(isCliApp, args);
        }

        app.UseAuthorization();

        app.MapControllers();

        app.MapHealthChecks("/health");
            
        await app.RunAsync();

        return 0;
    }
}