using Hangfire.Dashboard;
using Microsoft.Extensions.Caching.Memory;

namespace LineWebServer.Http.Authorization;

public class HangfireAsyncAuthorizationFilter(IMemoryCache cache, IConfiguration configuration): IDashboardAsyncAuthorizationFilter
{
    public Task<bool> AuthorizeAsync(DashboardContext context)
    {
        string? hfAuthToken = configuration["HangfireAuthToken"] ?? null;
        if (hfAuthToken is null)
        {
            return Task.FromResult(false);
        }
        
        var httpContext = context.GetHttpContext();

        const string cacheKey = "isHangfireAuthorized";
        
        if (cache.TryGetValue(cacheKey, out bool isAuthorized))
        {
            return Task.FromResult(isAuthorized);
        }

        bool isSecretValid = httpContext.Request.Query.TryGetValue("auth", out var authValue) && authValue == hfAuthToken;

        if (isSecretValid)
        {
            cache.Set(cacheKey, true, TimeSpan.FromMinutes(15));  // Cache for 15 minutes
        }

        return Task.FromResult(isSecretValid);
    }
}
