using System.Security.Cryptography;
using System.Text;
using Domain;
using LineWebServer.Application.Configs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace LineWebServer.Middlewares;

public class WorkerAuthenticationMiddleware(IProblemDetailsService problemDetailsService, RequestDelegate next, ILogger<WorkerAuthenticationMiddleware> logger, IOptions<PpmsApiConfig> workerApiKey)
{
    private readonly string _apiKey = workerApiKey.Value.ClientId ?? throw new InvalidOperationException("WorkerApiKey is missing from configuration.");
    private readonly string _secretKey = workerApiKey.Value.ClientSecret ?? throw new InvalidOperationException("WorkerSecretKey is missing from configuration.");

    public async Task InvokeAsync(HttpContext context)
    {
        if (!context.Request.Headers.TryGetValue("X-Api-Key", out var apiKey) || apiKey != _apiKey)
        {
            logger.LogWarning("Unauthorized: Invalid API Key");
            context.Response.StatusCode = StatusCodes.Status400BadRequest;

            await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
            {
                HttpContext = context,
                ProblemDetails = new ProblemDetails
                {
                    Status = 400,
                    Title = "Bad Request: Invalid API Key",
                }
            });
            return;
        }

        if (!context.Request.Headers.TryGetValue("X-Timestamp", out var timestampValues) ||
            !context.Request.Headers.TryGetValue("X-Signature", out var signatureValues))
        {
            logger.LogWarning("Unauthorized: Missing authentication headers");
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
            {
                HttpContext = context,
                ProblemDetails = new ProblemDetails
                {
                    Status = 400,
                    Title = "Bad Request: missing authentication header",
                }
            });
            return;
        }
            
        var timestamp = timestampValues.ToString();
        var signature = signatureValues.ToString();

        if (string.IsNullOrEmpty(timestamp) || string.IsNullOrEmpty(signature))
        {
            logger.LogWarning("Unauthorized: Empty authentication headers");
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
            {
                HttpContext = context,
                ProblemDetails = new ProblemDetails
                {
                    Status = 400,
                    Title = "Bad Request:Empty authentication headers",
                }
            });
            return;
        }

        if (!HmacProcessor.ValidateHmacSignature(_secretKey, context.Request.Method, context.Request.Path, timestamp, signature))
        {
            logger.LogWarning("Unauthorized: Invalid HMAC signature");
            context.Response.StatusCode = StatusCodes.Status403Forbidden;
            await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
            {
                HttpContext = context,
                ProblemDetails = new ProblemDetails
                {
                    Status = 403,
                    Title = "Unauthorized: Invalid HMAC signature",
                }
            });
            return;
        }
            
        await next(context);
    }
}