<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>LineWebServer</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.SmartEnum" Version="8.2.0" />
    <PackageReference Include="CliFx" Version="2.3.5" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="MQTTnet" Version="4.3.3.952" />
    <PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.3.952" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql.Design" Version="1.1.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Http\Response\" />
    <Folder Include="Migrations\" />
    <Folder Include="Services\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\LineWebServer.Application\LineWebServer.Application.csproj" />
    <ProjectReference Include="..\LineWebServer.Infra\LineWebServer.Infra.csproj" />
  </ItemGroup>

</Project>
