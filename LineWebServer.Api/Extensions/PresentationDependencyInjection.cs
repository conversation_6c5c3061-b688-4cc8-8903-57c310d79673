using System.Diagnostics;
using Domain;
using Domain.Contracts;
using Domain.Contracts.Services;
using Domain.Services;
using Hangfire;
using Hangfire.MySql;
using LineWebServer.Application.Configs;
using LineWebServer.Application.Layouts.Services;
using LineWebServer.Application.LineMonitors.Services;
using LineWebServer.Commands;
using LineWebServer.Infra.MqttWorker;
using Microsoft.AspNetCore.Http.Features;

namespace LineWebServer.Extensions;

public static class PresentationDependencyInjection
{
    public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddControllers();
       
        services.AddEndpointsApiExplorer();
       
        services.AddSwaggerGen(c =>
        {
            c.EnableAnnotations(); // Required!
        });
        
        services.AddMemoryCache();

        services.AddHealthChecks();
        
        return services;
    }

    public static IServiceCollection AddMqttBackgroundService(this IServiceCollection services, IConfiguration config, bool isCliApp)
    {
        if(isCliApp)
        {
            return services;
        }
        
        services.AddSingleton<IChannelQueueInterface<PublishToBrokerRequest>, UnboundedQueue<PublishToBrokerRequest>>();
        
        services.Configure<MqttConfig>(config.GetSection(MqttConfig.Key));
        
        services.Configure<List<FloorConfig>>(config.GetSection("Floors"));
        
        services.AddHostedService<MqttBackgroundService>();
        
        return services;
    }

    public static IServiceCollection AddProblemDetailsService(this IServiceCollection services, IConfiguration config)
    {
        services.AddProblemDetails(options =>
        {
            options.CustomizeProblemDetails = context =>
            {
                context.ProblemDetails.Instance =
                    $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";

                context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);

                Activity? activity = context.HttpContext.Features.Get<IHttpActivityFeature>()?.Activity;
                context.ProblemDetails.Extensions.TryAdd("traceId", activity?.Id);
                context.ProblemDetails.Extensions.TryAdd("exception", context.Exception);
            };
        });

        return services;
    }

    public static IServiceCollection AddCorsService(this IServiceCollection services, IConfiguration config)
    {
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy
                    .SetIsOriginAllowed(_ => true)
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
        });

        return services;
    }

    public static IServiceCollection AddCommands(this IServiceCollection services)
    {
        services.AddTransient<SeedStation>();
        
        services.AddTransient<SeedLayout>();

        return services;
    }   
}
