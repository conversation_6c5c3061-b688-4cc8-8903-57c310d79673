using System.Collections;
using CliFx;
using Hangfire;
using LineWebServer.Application.Jobs;
using LineWebServer.Http.Authorization;
using LineWebServer.Jobs;
using LineWebServer.Middlewares;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Caching.Memory;

namespace LineWebServer.Extensions;

public static class PresentationAppConfig
{
    public static WebApplication ConfigureApp(this WebApplication app)
    {
        app.UseStatusCodePages();
        
        app.UseWhen(context => context.Request.Path.StartsWithSegments("/worker-api"), appBuilder =>
        {
            appBuilder.UseMiddleware<WorkerAuthenticationMiddleware>();
        });

        // Add CORS middleware
        app.UseCors();

        if (app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Error");
            app.UseExceptionHandler(); 
        }
        
        app.UseSwagger();
        app.UseSwaggerUI();

        // Map health check endpoint
        app.MapHealthChecks("/health-check", new HealthCheckOptions
        {
            AllowCachingResponses = false
        });

        return app;
    }
    public static WebApplication ConfigureAppForHangfire(this WebApplication app, bool isCliApp)
    {
        if (isCliApp)
        {
            return app;
        }
        
        var option = new DashboardOptions
        {
            AsyncAuthorization = [new HangfireAsyncAuthorizationFilter(app.Services.GetRequiredService<IMemoryCache>(), app.Services.GetRequiredService<IConfiguration>())]
        };
        app.UseHangfireDashboard(options: option);
        
        var recurringJobManager = app.Services.GetRequiredService<IRecurringJobManager>();

        recurringJobManager.AddOrUpdate<ProcessHourlyOutput>(
            nameof(ProcessHourlyOutput),
            handler => handler.Handle(),
            Cron.Hourly
        );

        return app;
    }
    public static async Task<int> RunCliApplicationAsync(this WebApplication app, bool isCliApp, string[] args)
    {
        string[] cliArgs = args.Where(a => a != "--cli").ToArray();

        using IServiceScope scope = app.Services.CreateScope();
        IServiceProvider scopedProvider = scope.ServiceProvider;
            
        return await new CliApplicationBuilder()
            .SetExecutableName("line-webserver-cli") 
            .AddCommandsFromThisAssembly()
            .UseTypeActivator(T => scopedProvider.GetRequiredService(T))
            .Build()
            .RunAsync(cliArgs);
    }
}