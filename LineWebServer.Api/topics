OP Subscribe:
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/reset
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/worker_data"
Payload: n:name,e:0.00,y:0,g:0,r:0
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/eff
Payload: x.xx
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/gr
Payload: x
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/yl
Payload: x
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/rd
Payload: x

OP Publish:
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/on_sw
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/on_rfid_set
Payload: xxx
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/on_pd_mode
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/on_alter_mode
-------------------------------------
Topic: floor/{@event.Floor}/line/{@event.Line}/st/{@event.Station}/on_maintenance_mode
