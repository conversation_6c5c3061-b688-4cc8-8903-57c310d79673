{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.EntityFrameworkCore": "Information", "Hangfire": "Information"}}, "AllowedHosts": "*", "DefaultConnection": "Server=localhost;User=root;Password=;Database=ppms", "HangfireConnection": "server=localhost;uid=**;pwd=**;database=ppms;Allow User Variables=True", "HangfireAuthToken": "74484428529024539565991816030168", "WorkerApiKeys": {"ClientId": "74484428529024539565991816030168", "ClientSecret": "51181125161496600743167836387704"}, "Mqtt": {"Host": "localhost", "Port": 1883}, "OrgServer": {"BaseUrl": "http://localhost:5266", "ClientId": "Client Id From Org Server"}, "TotalStations": 20, "Floors": [{"No": 1, "Lines": [{"No": 1, "TotalStations": 20}]}], "LayoutPosition": {"FloorNo": 1, "LineNo": 1}}