using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Domain.Entities;
using LineWebServer.Infra.AppDbContext;
using Microsoft.EntityFrameworkCore;

namespace LineWebServer.Commands;

[Command("seed:layout", Description = "Seed Layouts")]
public class SeedLayout(ApplicationDbContext context) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        int totalCreatedStations = context.Stations.Count();

        if (totalCreatedStations == 0)
        {
            await console.Output.WriteLineAsync($"Please seed station first.");

            return;
        }
        
        
        Layout? layout = context.Layouts.FirstOrDefault(q => q.Label == "Default");

        if (layout == null)
        {
            layout = new Layout
            {
                Label = "Default",
                IsActive = true,
            };
            
            await context.Layouts.AddAsync(layout);
            await context.SaveChangesAsync();
        }
        await context.LayoutItems.Where(l => l.Id == layout.Id).ExecuteDeleteAsync();
        
        
        var stations = await context.Stations
            .Select(station => new { station.Id, station.Number })
            .ToListAsync();

        var layoutNo = 1;
        foreach (var station in stations)
        {
            await context.LayoutItems.AddAsync(new LayoutItem
            {
                LayoutId = layout.Id,
                StationId = station.Id,
                Number = layoutNo++,
                Type = $"Layout {station.Number}"
            });
        }
        
        
        await context.SaveChangesAsync();
    }
}
