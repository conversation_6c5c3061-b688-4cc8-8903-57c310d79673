using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Domain.Entities;
using LineWebServer.Infra.AppDbContext;

namespace LineWebServer.Commands;

[Command("seed:station", Description = "Seed stations")]
public class SeedStation(ApplicationDbContext context, IConfiguration configuration) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        string? totalStation = configuration["TotalStations"] ?? null;

        if (totalStation is null)
        {
            await console.Output.WriteLineAsync("Total station not found in configuration.");
            return;
        }

        if (!int.TryParse(totalStation, out int stationCount))
        {
            await console.Output.WriteLineAsync("Total station parsing failed");
            return;
        }

        for (int i = 1; i <= stationCount; i++)
        {
            Station? station = context.Stations.FirstOrDefault(s => s.Number == i);

            if (station is null)
            {
                await context.Stations.AddAsync(new Station
                {
                    Number = i,
                    Type = "default",
                    CurrentMode = "production",
                });
            }
        }
            
        await context.SaveChangesAsync();
        await console.Output.WriteLineAsync($"Total stations: {totalStation}");

        await console.Output.WriteLineAsync("All Station seeded properly");
    }
}
